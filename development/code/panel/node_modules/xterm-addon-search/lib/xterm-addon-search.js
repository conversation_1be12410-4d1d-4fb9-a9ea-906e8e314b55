!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SearchAddon=t():e.SearchAddon=t()}(self,(function(){return(()=>{"use strict";var e={258:function(e,t,i){var r=this&&this.__assign||function(){return r=Object.assign||function(e){for(var t,i=1,r=arguments.length;i<r;i++)for(var s in t=arguments[i])Object.prototype.hasOwnProperty.call(t,s)&&(e[s]=t[s]);return e},r.apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.SearchAddon=void 0;var s=i(345),n=" ~!@#$%^&*()+`-=[]{}|\\;:\"',./<>?",o=function(){function e(){this._linesCacheTimeoutId=0,this._onDidChangeResults=new s.EventEmitter,this.onDidChangeResults=this._onDidChangeResults.event}return e.prototype.activate=function(e){var t=this;this._terminal=e,this._onDataDisposable=this._terminal.onWriteParsed((function(){return t._updateMatches()})),this._onResizeDisposable=this._terminal.onResize((function(){return t._updateMatches()}))},e.prototype._updateMatches=function(){var e,t=this;this._highlightTimeout&&window.clearTimeout(this._highlightTimeout),this._cachedSearchTerm&&(null===(e=this._lastSearchOptions)||void 0===e?void 0:e.decorations)&&(this._highlightTimeout=setTimeout((function(){var e,i;t.findPrevious(t._cachedSearchTerm,r(r({},t._lastSearchOptions),{incremental:!0,noScroll:!0})),t._resultIndex=t._searchResults?t._searchResults.size-1:-1,t._onDidChangeResults.fire({resultIndex:t._resultIndex,resultCount:null!==(i=null===(e=t._searchResults)||void 0===e?void 0:e.size)&&void 0!==i?i:-1})}),200))},e.prototype.dispose=function(){var e,t;this.clearDecorations(),null===(e=this._onDataDisposable)||void 0===e||e.dispose(),null===(t=this._onResizeDisposable)||void 0===t||t.dispose()},e.prototype.clearDecorations=function(e){var t,i,r,s;null===(t=this._selectedDecoration)||void 0===t||t.dispose(),null===(i=this._searchResults)||void 0===i||i.clear(),null===(r=this._resultDecorations)||void 0===r||r.forEach((function(e){for(var t=0,i=e;t<i.length;t++)i[t].dispose()})),null===(s=this._resultDecorations)||void 0===s||s.clear(),this._searchResults=void 0,this._resultDecorations=void 0,e||(this._cachedSearchTerm=void 0)},e.prototype.clearActiveDecoration=function(){var e;null===(e=this._selectedDecoration)||void 0===e||e.dispose(),this._selectedDecoration=void 0},e.prototype.findNext=function(e,t){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");return this._lastSearchOptions=t,(null==t?void 0:t.decorations)&&(void 0===this._resultIndex&&void 0!==this._cachedSearchTerm&&e===this._cachedSearchTerm||this._highlightAllMatches(e,t)),this._fireResults(e,this._findNextAndSelect(e,t),t)},e.prototype._highlightAllMatches=function(e,t){var i=this;if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(e&&0!==e.length){t=t||{},this.clearDecorations(!0),this._searchResults=new Map,this._resultDecorations=new Map;for(var r=this._resultDecorations,s=this._find(e,0,0,t);s&&!this._searchResults.get(s.row+"-"+s.col);)if(this._searchResults.set(s.row+"-"+s.col,s),s=this._find(e,s.col+s.term.length>=this._terminal.cols?s.row+1:s.row,s.col+s.term.length>=this._terminal.cols?0:s.col+1,t),this._searchResults.size>1e3)return this.clearDecorations(),void(this._resultIndex=void 0);this._searchResults.forEach((function(e){var s=i._createResultDecoration(e,t.decorations);if(s){var n=r.get(s.marker.line)||[];n.push(s),r.set(s.marker.line,n)}}))}else this.clearDecorations()},e.prototype._find=function(e,t,i,r){var s;if(!this._terminal||!e||0===e.length)return null===(s=this._terminal)||void 0===s||s.clearSelection(),void this.clearDecorations();if(i>this._terminal.cols)throw new Error("Invalid col: "+i+" to search in terminal of "+this._terminal.cols+" cols");var n=void 0;this._initLinesCache();var o={startRow:t,startCol:i};if(!(n=this._findInLine(e,o,r)))for(var l=t+1;l<this._terminal.buffer.active.baseY+this._terminal.rows&&(o.startRow=l,o.startCol=0,!(n=this._findInLine(e,o,r)));l++);return n},e.prototype._findNextAndSelect=function(e,t){var i;if(!this._terminal||!e||0===e.length)return null===(i=this._terminal)||void 0===i||i.clearSelection(),this.clearDecorations(),this._cachedSearchTerm=void 0,this._resultIndex=-1,!1;this._cachedSearchTerm!==e&&(this._resultIndex=void 0,this._terminal.clearSelection());var r,s=0,n=0;if(this._terminal.hasSelection()){var o=!!t&&t.incremental;r=this._terminal.getSelectionPosition(),n=o?r.startRow:r.endRow,s=o?r.startColumn:r.endColumn}this._initLinesCache();var l={startRow:n,startCol:s},a=this._findInLine(e,l,t);if(!a)for(var h=n+1;h<this._terminal.buffer.active.baseY+this._terminal.rows&&(l.startRow=h,l.startCol=0,!(a=this._findInLine(e,l,t)));h++);if(!a&&0!==n)for(h=0;h<n&&(l.startRow=h,l.startCol=0,!(a=this._findInLine(e,l,t)));h++);return!a&&r&&(l.startRow=r.startRow,l.startCol=0,a=this._findInLine(e,l,t)),this._searchResults&&(0===this._searchResults.size?this._resultIndex=-1:void 0===this._resultIndex?this._resultIndex=0:(this._resultIndex++,this._resultIndex>=this._searchResults.size&&(this._resultIndex=0))),this._selectResult(a,null==t?void 0:t.decorations,null==t?void 0:t.noScroll)},e.prototype.findPrevious=function(e,t){if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");return this._lastSearchOptions=t,(null==t?void 0:t.decorations)&&(void 0===this._resultIndex&&void 0!==this._cachedSearchTerm&&e===this._cachedSearchTerm||this._highlightAllMatches(e,t)),this._fireResults(e,this._findPreviousAndSelect(e,t),t)},e.prototype._fireResults=function(e,t,i){var r;return(null==i?void 0:i.decorations)&&(void 0!==this._resultIndex&&void 0!==(null===(r=this._searchResults)||void 0===r?void 0:r.size)?this._onDidChangeResults.fire({resultIndex:this._resultIndex,resultCount:this._searchResults.size}):this._onDidChangeResults.fire(void 0)),this._cachedSearchTerm=e,t},e.prototype._findPreviousAndSelect=function(e,t){var i,r;if(!this._terminal)throw new Error("Cannot use addon until it has been loaded");if(!this._terminal||!e||0===e.length)return r=void 0,null===(i=this._terminal)||void 0===i||i.clearSelection(),this.clearDecorations(),this._resultIndex=-1,!1;this._cachedSearchTerm!==e&&(this._resultIndex=void 0,this._terminal.clearSelection());var s,n=this._terminal.buffer.active.baseY+this._terminal.rows,o=this._terminal.cols,l=!0,a=!!t&&t.incremental;this._terminal.hasSelection()&&(n=(s=this._terminal.getSelectionPosition()).startRow,o=s.startColumn),this._initLinesCache();var h={startRow:n,startCol:o};if(a?(r=this._findInLine(e,h,t,!1))&&r.row===n&&r.col===o||(s&&(h.startRow=s.endRow,h.startCol=s.endColumn),r=this._findInLine(e,h,t,!0)):r=this._findInLine(e,h,t,l),!r){h.startCol=Math.max(h.startCol,this._terminal.cols);for(var c=n-1;c>=0&&(h.startRow=c,!(r=this._findInLine(e,h,t,l)));c--);}if(!r&&n!==this._terminal.buffer.active.baseY+this._terminal.rows)for(c=this._terminal.buffer.active.baseY+this._terminal.rows;c>=n&&(h.startRow=c,!(r=this._findInLine(e,h,t,l)));c--);return this._searchResults&&(0===this._searchResults.size?this._resultIndex=-1:void 0===this._resultIndex||this._resultIndex<0?this._resultIndex=this._searchResults.size-1:(this._resultIndex--,-1===this._resultIndex&&(this._resultIndex=this._searchResults.size-1))),!(r||!s)||this._selectResult(r,null==t?void 0:t.decorations,null==t?void 0:t.noScroll)},e.prototype._initLinesCache=function(){var e=this,t=this._terminal;this._linesCache||(this._linesCache=new Array(t.buffer.active.length),this._cursorMoveListener=t.onCursorMove((function(){return e._destroyLinesCache()})),this._resizeListener=t.onResize((function(){return e._destroyLinesCache()}))),window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=window.setTimeout((function(){return e._destroyLinesCache()}),15e3)},e.prototype._destroyLinesCache=function(){this._linesCache=void 0,this._cursorMoveListener&&(this._cursorMoveListener.dispose(),this._cursorMoveListener=void 0),this._resizeListener&&(this._resizeListener.dispose(),this._resizeListener=void 0),this._linesCacheTimeoutId&&(window.clearTimeout(this._linesCacheTimeoutId),this._linesCacheTimeoutId=0)},e.prototype._isWholeWord=function(e,t,i){return(0===e||n.includes(t[e-1]))&&(e+i.length===t.length||n.includes(t[e+i.length]))},e.prototype._findInLine=function(e,t,i,r){var s;void 0===i&&(i={}),void 0===r&&(r=!1);var n=this._terminal,o=t.startRow,l=t.startCol,a=n.buffer.active.getLine(o);if(null==a?void 0:a.isWrapped)return r?void(t.startCol+=n.cols):(t.startRow--,t.startCol+=n.cols,this._findInLine(e,t,i));var h=null===(s=this._linesCache)||void 0===s?void 0:s[o];h||(h=this._translateBufferLineToStringWithWrap(o,!0),this._linesCache&&(this._linesCache[o]=h));var c=h[0],u=h[1],d=this._bufferColsToStringOffset(o,l),_=i.caseSensitive?e:e.toLowerCase(),f=i.caseSensitive?c:c.toLowerCase(),v=-1;if(i.regex){var p=RegExp(_,"g"),g=void 0;if(r)for(;g=p.exec(f.slice(0,d));)v=p.lastIndex-g[0].length,e=g[0],p.lastIndex-=e.length-1;else(g=p.exec(f.slice(d)))&&g[0].length>0&&(v=d+(p.lastIndex-g[0].length),e=g[0])}else r?d-_.length>=0&&(v=f.lastIndexOf(_,d-_.length)):v=f.indexOf(_,d);if(v>=0){if(i.wholeWord&&!this._isWholeWord(v,f,e))return;for(var m=0;m<u.length-1&&v>=u[m+1];)m++;for(var w=m;w<u.length-1&&v+e.length>=u[w+1];)w++;var C=v-u[m],R=v+e.length-u[w],x=this._stringLengthToBufferSize(o+m,C);return{term:e,col:x,row:o+m,size:this._stringLengthToBufferSize(o+w,R)-x+n.cols*(w-m)}}},e.prototype._stringLengthToBufferSize=function(e,t){var i=this._terminal.buffer.active.getLine(e);if(!i)return 0;for(var r=0;r<t;r++){var s=i.getCell(r);if(!s)break;var n=s.getChars();n.length>1&&(t-=n.length-1);var o=i.getCell(r+1);o&&0===o.getWidth()&&t++}return t},e.prototype._bufferColsToStringOffset=function(e,t){for(var i=this._terminal,r=e,s=0,n=i.buffer.active.getLine(r);t>0&&n;){for(var o=0;o<t&&o<i.cols;o++){var l=n.getCell(o);if(!l)break;l.getWidth()&&(s+=0===l.getCode()?1:l.getChars().length)}if(r++,(n=i.buffer.active.getLine(r))&&!n.isWrapped)break;t-=i.cols}return s},e.prototype._translateBufferLineToStringWithWrap=function(e,t){for(var i,r=this._terminal,s=[],n=[0],o=r.buffer.active.getLine(e);o;){var l=r.buffer.active.getLine(e+1),a=!!l&&l.isWrapped,h=o.translateToString(!a&&t);if(a&&l){var c=o.getCell(o.length-1);c&&0===c.getCode()&&1===c.getWidth()&&2===(null===(i=l.getCell(0))||void 0===i?void 0:i.getWidth())&&(h=h.slice(0,-1))}if(s.push(h),!a)break;n.push(n[n.length-1]+h.length),e++,o=l}return[s.join(""),n]},e.prototype._selectResult=function(e,t,i){var r,s,n=this,o=this._terminal;if(this.clearActiveDecoration(),!e)return o.clearSelection(),!1;if(o.select(e.col,e.row,e.size),t){var l=o.registerMarker(-o.buffer.active.baseY-o.buffer.active.cursorY+e.row);l&&(this._selectedDecoration=o.registerDecoration({marker:l,x:e.col,width:e.size,backgroundColor:t.activeMatchBackground,layer:"top",overviewRulerOptions:{color:t.activeMatchColorOverviewRuler}}),null===(r=this._selectedDecoration)||void 0===r||r.onRender((function(e){return n._applyStyles(e,t.activeMatchBorder,!0)})),null===(s=this._selectedDecoration)||void 0===s||s.onDispose((function(){return l.dispose()})))}if(!i&&(e.row>=o.buffer.active.viewportY+o.rows||e.row<o.buffer.active.viewportY)){var a=e.row-o.buffer.active.viewportY;a-=Math.floor(o.rows/2),o.scrollLines(a)}return!0},e.prototype._applyStyles=function(e,t,i){e.clientWidth<=0||(e.classList.contains("xterm-find-result-decoration")||(e.classList.add("xterm-find-result-decoration"),t&&(e.style.outline="1px solid "+t)),i&&e.classList.add("xterm-find-active-result-decoration"))},e.prototype._createResultDecoration=function(e,t){var i,r=this,s=this._terminal,n=s.registerMarker(-s.buffer.active.baseY-s.buffer.active.cursorY+e.row);if(n){var o=s.registerDecoration({marker:n,x:e.col,width:e.size,backgroundColor:t.matchBackground,overviewRulerOptions:(null===(i=this._resultDecorations)||void 0===i?void 0:i.get(n.line))?void 0:{color:t.matchOverviewRuler,position:"center"}});return null==o||o.onRender((function(e){return r._applyStyles(e,t.matchBorder,!1)})),null==o||o.onDispose((function(){return n.dispose()})),o}},e}();t.SearchAddon=o},345:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.forwardEvent=t.EventEmitter=void 0;var i=function(){function e(){this._listeners=[],this._disposed=!1}return Object.defineProperty(e.prototype,"event",{get:function(){var e=this;return this._event||(this._event=function(t){return e._listeners.push(t),{dispose:function(){if(!e._disposed)for(var i=0;i<e._listeners.length;i++)if(e._listeners[i]===t)return void e._listeners.splice(i,1)}}}),this._event},enumerable:!1,configurable:!0}),e.prototype.fire=function(e,t){for(var i=[],r=0;r<this._listeners.length;r++)i.push(this._listeners[r]);for(r=0;r<i.length;r++)i[r].call(void 0,e,t)},e.prototype.dispose=function(){this._listeners&&(this._listeners.length=0),this._disposed=!0},e}();t.EventEmitter=i,t.forwardEvent=function(e,t){return e((function(e){return t.fire(e)}))}}},t={};return function i(r){var s=t[r];if(void 0!==s)return s.exports;var n=t[r]={exports:{}};return e[r].call(n.exports,n,n.exports,i),n.exports}(258)})()}));
//# sourceMappingURL=xterm-addon-search.js.map