{"version": 3, "file": "xterm-addon-search.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,IARxB,CASGK,MAAM,WACT,M,6VCJA,aA2CMC,EAAsB,qCAG5B,0BAgBU,KAAAC,qBAAuB,EAMd,KAAAC,oBAAsB,IAAI,EAAAC,aAC3B,KAAAC,mBAAqBC,KAAKH,oBAAoBI,MAoqBhE,OAlqBS,YAAAC,SAAP,SAAgBC,GAAhB,WACEH,KAAKI,UAAYD,EACjBH,KAAKK,kBAAoBL,KAAKI,UAAUE,eAAc,WAAM,SAAKC,oBACjEP,KAAKQ,oBAAsBR,KAAKI,UAAUK,UAAS,WAAM,SAAKF,qBAGxD,YAAAA,eAAR,e,EAAA,OACMP,KAAKU,mBACPC,OAAOC,aAAaZ,KAAKU,mBAEvBV,KAAKa,oBAA4C,QAAvB,EAAAb,KAAKc,0BAAkB,eAAEC,eACrDf,KAAKU,kBAAoBM,YAAW,W,QAClC,EAAKC,aAAa,EAAKJ,kBAAoB,EAAF,KAAO,EAAKC,oBAAkB,CAAEI,aAAa,EAAMC,UAAU,KACtG,EAAKC,aAAe,EAAKC,eAAiB,EAAKA,eAAeC,KAAO,GAAK,EAC1E,EAAKzB,oBAAoB0B,KAAK,CAAEC,YAAa,EAAKJ,aAAcK,YAAsC,QAAzB,EAAmB,QAAnB,IAAKJ,sBAAc,eAAEC,YAAI,SAAK,MAC1G,OAIA,YAAAI,QAAP,W,QACE1B,KAAK2B,mBACiB,QAAtB,EAAA3B,KAAKK,yBAAiB,SAAEqB,UACA,QAAxB,EAAA1B,KAAKQ,2BAAmB,SAAEkB,WAGrB,YAAAC,iBAAP,SAAwBC,G,YACE,QAAxB,EAAA5B,KAAK6B,2BAAmB,SAAEH,UACP,QAAnB,EAAA1B,KAAKqB,sBAAc,SAAES,QACE,QAAvB,EAAA9B,KAAK+B,0BAAkB,SAAEC,SAAQ,SAAAjB,GAC/B,IAAgB,UAAAA,EAAA,eAAJ,KACRW,aAGiB,QAAvB,EAAA1B,KAAK+B,0BAAkB,SAAED,QACzB9B,KAAKqB,oBAAiBY,EACtBjC,KAAK+B,wBAAqBE,EACrBL,IACH5B,KAAKa,uBAAoBoB,IAItB,YAAAC,sBAAP,W,MAC0B,QAAxB,EAAAlC,KAAK6B,2BAAmB,SAAEH,UAC1B1B,KAAK6B,yBAAsBI,GAUtB,YAAAE,SAAP,SAAgBC,EAAcC,GAC5B,IAAKrC,KAAKI,UACR,MAAM,IAAIkC,MAAM,6CAQlB,OANAtC,KAAKc,mBAAqBuB,GACtBA,MAAAA,OAAa,EAAbA,EAAetB,oBACSkB,IAAtBjC,KAAKoB,mBAAyDa,IAA3BjC,KAAKa,mBAAmCuB,IAASpC,KAAKa,mBAC3Fb,KAAKuC,qBAAqBH,EAAMC,IAG7BrC,KAAKwC,aAAaJ,EAAMpC,KAAKyC,mBAAmBL,EAAMC,GAAgBA,IAGvE,YAAAE,qBAAR,SAA6BH,EAAcC,GAA3C,WACE,IAAKrC,KAAKI,UACR,MAAM,IAAIkC,MAAM,6CAElB,GAAKF,GAAwB,IAAhBA,EAAKM,OAAlB,CAIAL,EAAgBA,GAAiB,GAGjCrC,KAAK2B,kBAAiB,GACtB3B,KAAKqB,eAAiB,IAAIsB,IAC1B3C,KAAK+B,mBAAqB,IAAIY,IAG9B,IAFA,IAAMC,EAAoB5C,KAAK+B,mBAC3Bc,EAAS7C,KAAK8C,MAAMV,EAAM,EAAG,EAAGC,GAC7BQ,IAAW7C,KAAKqB,eAAe0B,IAAOF,EAAOG,IAAG,IAAIH,EAAOI,MAQhE,GAPAjD,KAAKqB,eAAe6B,IAAOL,EAAOG,IAAG,IAAIH,EAAOI,IAAOJ,GACvDA,EAAS7C,KAAK8C,MACZV,EACAS,EAAOI,IAAMJ,EAAOT,KAAKM,QAAU1C,KAAKI,UAAU+C,KAAON,EAAOG,IAAM,EAAIH,EAAOG,IACjFH,EAAOI,IAAMJ,EAAOT,KAAKM,QAAU1C,KAAKI,UAAU+C,KAAO,EAAIN,EAAOI,IAAM,EAC1EZ,GAEErC,KAAKqB,eAAeC,KAAO,IAG7B,OAFAtB,KAAK2B,wBACL3B,KAAKoB,kBAAea,GAIxBjC,KAAKqB,eAAeW,SAAQ,SAAAa,GAC1B,IAAMO,EAAmB,EAAKC,wBAAwBR,EAAQR,EAActB,aAC5E,GAAIqC,EAAkB,CACpB,IAAME,EAAqBV,EAAkBG,IAAIK,EAAiBG,OAAOC,OAAS,GAClFF,EAAmBG,KAAKL,GACxBR,EAAkBM,IAAIE,EAAiBG,OAAOC,KAAMF,YA9BtDtD,KAAK2B,oBAmCD,YAAAmB,MAAR,SAAcV,EAAcsB,EAAkBC,EAAkBtB,G,MAC9D,IAAKrC,KAAKI,YAAcgC,GAAwB,IAAhBA,EAAKM,OAGnC,OAFc,QAAd,EAAA1C,KAAKI,iBAAS,SAAEwD,sBAChB5D,KAAK2B,mBAGP,GAAIgC,EAAW3D,KAAKI,UAAU+C,KAC5B,MAAM,IAAIb,MAAM,gBAAgBqB,EAAQ,6BAA6B3D,KAAKI,UAAU+C,KAAI,SAG1F,IAAIN,OAAoCZ,EAExCjC,KAAK6D,kBAEL,IAAMC,EAAkC,CACtCJ,SAAQ,EACRC,SAAQ,GAMV,KAFAd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,IAI9C,IAAK,IAAI2B,EAAIN,EAAW,EAAGM,EAAIhE,KAAKI,UAAU6D,OAAOC,OAAOC,MAAQnE,KAAKI,UAAUgE,OACjFN,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,IAG1Bd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,KALuC2B,KAW3F,OAAOnB,GAGD,YAAAJ,mBAAR,SAA2BL,EAAcC,G,MACvC,IAAKrC,KAAKI,YAAcgC,GAAwB,IAAhBA,EAAKM,OAKnC,OAJc,QAAd,EAAA1C,KAAKI,iBAAS,SAAEwD,iBAChB5D,KAAK2B,mBACL3B,KAAKa,uBAAoBoB,EACzBjC,KAAKoB,cAAgB,GACd,EAGLpB,KAAKa,oBAAsBuB,IAC7BpC,KAAKoB,kBAAea,EACpBjC,KAAKI,UAAUwD,kBAGjB,IAEIS,EAFAV,EAAW,EACXD,EAAW,EAEf,GAAI1D,KAAKI,UAAUkE,eAAgB,CACjC,IAAMpD,IAAcmB,GAAgBA,EAAcnB,YAGlDmD,EAAmBrE,KAAKI,UAAUmE,uBAClCb,EAAWxC,EAAcmD,EAAiBX,SAAWW,EAAiBG,OACtEb,EAAWzC,EAAcmD,EAAiBI,YAAcJ,EAAiBK,UAG3E1E,KAAK6D,kBAEL,IAAMC,EAAkC,CACtCJ,SAAQ,EACRC,SAAQ,GAINd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,GAEpD,IAAKQ,EAEH,IAAK,IAAImB,EAAIN,EAAW,EAAGM,EAAIhE,KAAKI,UAAU6D,OAAOC,OAAOC,MAAQnE,KAAKI,UAAUgE,OACjFN,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,IAG1Bd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,KALuC2B,KAY3F,IAAKnB,GAAuB,IAAba,EACb,IAASM,EAAI,EAAGA,EAAIN,IAClBI,EAAeJ,SAAWM,EAC1BF,EAAeH,SAAW,IAC1Bd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,KAHpB2B,KA8BhC,OAnBKnB,GAAUwB,IACbP,EAAeJ,SAAWW,EAAiBX,SAC3CI,EAAeH,SAAW,EAC1Bd,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,IAG9CrC,KAAKqB,iBAC0B,IAA7BrB,KAAKqB,eAAeC,KACtBtB,KAAKoB,cAAgB,OACUa,IAAtBjC,KAAKoB,aACdpB,KAAKoB,aAAe,GAEpBpB,KAAKoB,eACDpB,KAAKoB,cAAgBpB,KAAKqB,eAAeC,OAC3CtB,KAAKoB,aAAe,KAKnBpB,KAAK2E,cAAc9B,EAAQR,MAAAA,OAAa,EAAbA,EAAetB,YAAasB,MAAAA,OAAa,EAAbA,EAAelB,WASxE,YAAAF,aAAP,SAAoBmB,EAAcC,GAChC,IAAKrC,KAAKI,UACR,MAAM,IAAIkC,MAAM,6CAQlB,OANAtC,KAAKc,mBAAqBuB,GACtBA,MAAAA,OAAa,EAAbA,EAAetB,oBACSkB,IAAtBjC,KAAKoB,mBAAyDa,IAA3BjC,KAAKa,mBAAmCuB,IAASpC,KAAKa,mBAC3Fb,KAAKuC,qBAAqBH,EAAMC,IAG7BrC,KAAKwC,aAAaJ,EAAMpC,KAAK4E,uBAAuBxC,EAAMC,GAAgBA,IAG3E,YAAAG,aAAR,SAAqBJ,EAAcyC,EAAgBxC,G,MASjD,OARIA,MAAAA,OAAa,EAAbA,EAAetB,oBACSkB,IAAtBjC,KAAKoB,mBAA4Da,KAAX,QAAnB,EAAAjC,KAAKqB,sBAAc,eAAEC,MAC1DtB,KAAKH,oBAAoB0B,KAAK,CAAEC,YAAaxB,KAAKoB,aAAcK,YAAazB,KAAKqB,eAAeC,OAEjGtB,KAAKH,oBAAoB0B,UAAKU,IAGlCjC,KAAKa,kBAAoBuB,EAClByC,GAGD,YAAAD,uBAAR,SAA+BxC,EAAcC,G,MAIvCQ,EAHJ,IAAK7C,KAAKI,UACR,MAAM,IAAIkC,MAAM,6CAGlB,IAAKtC,KAAKI,YAAcgC,GAAwB,IAAhBA,EAAKM,OAKnC,OAJAG,OAASZ,EACK,QAAd,EAAAjC,KAAKI,iBAAS,SAAEwD,iBAChB5D,KAAK2B,mBACL3B,KAAKoB,cAAgB,GACd,EAGLpB,KAAKa,oBAAsBuB,IAC7BpC,KAAKoB,kBAAea,EACpBjC,KAAKI,UAAUwD,kBAGjB,IAKIS,EALAX,EAAW1D,KAAKI,UAAU6D,OAAOC,OAAOC,MAAQnE,KAAKI,UAAUgE,KAC/DT,EAAW3D,KAAKI,UAAU+C,KACxB2B,GAAkB,EAElB5D,IAAcmB,GAAgBA,EAAcnB,YAE9ClB,KAAKI,UAAUkE,iBAGjBZ,GAFAW,EAAmBrE,KAAKI,UAAUmE,wBAENb,SAC5BC,EAAWU,EAAiBI,aAG9BzE,KAAK6D,kBACL,IAAMC,EAAkC,CACtCJ,SAAQ,EACRC,SAAQ,GAoBV,GAjBIzC,GAEF2B,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,GAAe,KACtBQ,EAAOG,MAAQU,GAAYb,EAAOI,MAAQU,IAG7EU,IACFP,EAAeJ,SAAWW,EAAiBG,OAC3CV,EAAeH,SAAWU,EAAiBK,WAE7C7B,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,GAAe,IAGjEQ,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,EAAeyC,IAI5DjC,EAAQ,CACXiB,EAAeH,SAAWoB,KAAKC,IAAIlB,EAAeH,SAAU3D,KAAKI,UAAU+C,MAC3E,IAAK,IAAIa,EAAIN,EAAW,EAAGM,GAAK,IAC9BF,EAAeJ,SAAWM,IAC1BnB,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,EAAeyC,KAF9Bd,MASrC,IAAKnB,GAAUa,IAAc1D,KAAKI,UAAU6D,OAAOC,OAAOC,MAAQnE,KAAKI,UAAUgE,KAC/E,IAASJ,EAAKhE,KAAKI,UAAU6D,OAAOC,OAAOC,MAAQnE,KAAKI,UAAUgE,KAAOJ,GAAKN,IAC5EI,EAAeJ,SAAWM,IAC1BnB,EAAS7C,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,EAAeyC,KAFuBd,KAuB1F,OAdIhE,KAAKqB,iBAC0B,IAA7BrB,KAAKqB,eAAeC,KACtBtB,KAAKoB,cAAgB,OACUa,IAAtBjC,KAAKoB,cAA8BpB,KAAKoB,aAAe,EAChEpB,KAAKoB,aAAepB,KAAKqB,eAAeC,KAAO,GAE/CtB,KAAKoB,gBACsB,IAAvBpB,KAAKoB,eACPpB,KAAKoB,aAAepB,KAAKqB,eAAeC,KAAO,OAMhDuB,IAAUwB,IAGRrE,KAAK2E,cAAc9B,EAAQR,MAAAA,OAAa,EAAbA,EAAetB,YAAasB,MAAAA,OAAa,EAAbA,EAAelB,WAMvE,YAAA0C,gBAAR,sBACQ1D,EAAWH,KAAKI,UACjBJ,KAAKiF,cACRjF,KAAKiF,YAAc,IAAIC,MAAM/E,EAAS8D,OAAOC,OAAOxB,QACpD1C,KAAKmF,oBAAsBhF,EAASiF,cAAa,WAAM,SAAKC,wBAC5DrF,KAAKsF,gBAAkBnF,EAASM,UAAS,WAAM,SAAK4E,yBAGtD1E,OAAOC,aAAaZ,KAAKJ,sBACzBI,KAAKJ,qBAAuBe,OAAOK,YAAW,WAAM,SAAKqE,uBAzY5B,OA4YvB,YAAAA,mBAAR,WACErF,KAAKiF,iBAAchD,EACfjC,KAAKmF,sBACPnF,KAAKmF,oBAAoBzD,UACzB1B,KAAKmF,yBAAsBlD,GAEzBjC,KAAKsF,kBACPtF,KAAKsF,gBAAgB5D,UACrB1B,KAAKsF,qBAAkBrD,GAErBjC,KAAKJ,uBACPe,OAAOC,aAAaZ,KAAKJ,sBACzBI,KAAKJ,qBAAuB,IAUxB,YAAA2F,aAAR,SAAqBC,EAAqBhC,EAAcpB,GACtD,OAAyB,IAAhBoD,GAAuB7F,EAAoB8F,SAASjC,EAAKgC,EAAc,OAC3EA,EAAcpD,EAAKM,SAAYc,EAAKd,QAAY/C,EAAoB8F,SAASjC,EAAKgC,EAAcpD,EAAKM,WAclG,YAAAqB,YAAV,SAAsB3B,EAAc0B,EAAiCzB,EAAoCyC,G,WAApC,IAAAzC,IAAAA,EAAA,SAAoC,IAAAyC,IAAAA,GAAA,GACvG,IAAM3E,EAAWH,KAAKI,UAChB4C,EAAMc,EAAeJ,SACrBT,EAAMa,EAAeH,SAGrB+B,EAAYvF,EAAS8D,OAAOC,OAAOyB,QAAQ3C,GACjD,GAAI0C,MAAAA,OAAS,EAATA,EAAWE,UACb,OAAId,OACFhB,EAAeH,UAAYxD,EAASgD,OAMtCW,EAAeJ,WACfI,EAAeH,UAAYxD,EAASgD,KAC7BnD,KAAK+D,YAAY3B,EAAM0B,EAAgBzB,IAEhD,IAAIwD,EAAwB,QAAhB,EAAA7F,KAAKiF,mBAAW,eAAGjC,GAC1B6C,IACHA,EAAQ7F,KAAK8F,qCAAqC9C,GAAK,GACnDhD,KAAKiF,cACPjF,KAAKiF,YAAYjC,GAAO6C,IAGrB,IAAAE,EAAuBF,EAAK,GAAhBG,EAAWH,EAAK,GAE7BI,EAASjG,KAAKkG,0BAA0BlD,EAAKC,GAC7CkD,EAAa9D,EAAc+D,cAAgBhE,EAAOA,EAAKiE,cACvDC,EAAmBjE,EAAc+D,cAAgBL,EAAaA,EAAWM,cAE3E7E,GAAe,EACnB,GAAIa,EAAckE,MAAO,CACvB,IAAMC,EAAcC,OAAON,EAAY,KACnCO,OAAS,EACb,GAAI5B,EAEF,KAAO4B,EAAYF,EAAYG,KAAKL,EAAiBM,MAAM,EAAGX,KAC5DzE,EAAcgF,EAAYK,UAAYH,EAAU,GAAGhE,OACnDN,EAAOsE,EAAU,GACjBF,EAAYK,WAAczE,EAAKM,OAAS,OAG1CgE,EAAYF,EAAYG,KAAKL,EAAiBM,MAAMX,MACnCS,EAAU,GAAGhE,OAAS,IACrClB,EAAcyE,GAAUO,EAAYK,UAAYH,EAAU,GAAGhE,QAC7DN,EAAOsE,EAAU,SAIjB5B,EACEmB,EAASE,EAAWzD,QAAU,IAChClB,EAAc8E,EAAiBQ,YAAYX,EAAYF,EAASE,EAAWzD,SAG7ElB,EAAc8E,EAAiBS,QAAQZ,EAAYF,GAIvD,GAAIzE,GAAe,EAAG,CACpB,GAAIa,EAAc2E,YAAchH,KAAKuF,aAAa/D,EAAa8E,EAAkBlE,GAC/E,OAKF,IADA,IAAI6E,EAAiB,EACdA,EAAiBjB,EAAQtD,OAAS,GAAKlB,GAAewE,EAAQiB,EAAiB,IACpFA,IAGF,IADA,IAAIC,EAAeD,EACZC,EAAelB,EAAQtD,OAAS,GAAKlB,EAAcY,EAAKM,QAAUsD,EAAQkB,EAAe,IAC9FA,IAEF,IAAMC,EAAiB3F,EAAcwE,EAAQiB,GACvCG,EAAe5F,EAAcY,EAAKM,OAASsD,EAAQkB,GACnDG,EAAgBrH,KAAKsH,0BAA0BtE,EAAMiE,EAAgBE,GAI3E,MAAO,CACL/E,KAAI,EACJa,IAAKoE,EACLrE,IAAKA,EAAMiE,EACX3F,KAPkBtB,KAAKsH,0BAA0BtE,EAAMkE,EAAcE,GAC5CC,EAAgBlH,EAASgD,MAAQ+D,EAAeD,MAWvE,YAAAK,0BAAR,SAAkCtE,EAAaiD,GAC7C,IAAMzC,EAAOxD,KAAKI,UAAW6D,OAAOC,OAAOyB,QAAQ3C,GACnD,IAAKQ,EACH,OAAO,EAET,IAAK,IAAI+D,EAAI,EAAGA,EAAItB,EAAQsB,IAAK,CAC/B,IAAMC,EAAOhE,EAAKiE,QAAQF,GAC1B,IAAKC,EACH,MAGF,IAAME,EAAOF,EAAKG,WACdD,EAAKhF,OAAS,IAChBuD,GAAUyB,EAAKhF,OAAS,GAI1B,IAAMkF,EAAWpE,EAAKiE,QAAQF,EAAI,GAC9BK,GAAoC,IAAxBA,EAASC,YACvB5B,IAGJ,OAAOA,GAGD,YAAAC,0BAAR,SAAkCxC,EAAkBP,GAKlD,IAJA,IAAMhD,EAAWH,KAAKI,UAClB0H,EAAYpE,EACZuC,EAAS,EACTzC,EAAOrD,EAAS8D,OAAOC,OAAOyB,QAAQmC,GACnC3E,EAAO,GAAKK,GAAM,CACvB,IAAK,IAAI+D,EAAI,EAAGA,EAAIpE,GAAQoE,EAAIpH,EAASgD,KAAMoE,IAAK,CAClD,IAAMC,EAAOhE,EAAKiE,QAAQF,GAC1B,IAAKC,EACH,MAEEA,EAAKK,aAEP5B,GAA6B,IAAnBuB,EAAKO,UAAkB,EAAIP,EAAKG,WAAWjF,QAKzD,GAFAoF,KACAtE,EAAOrD,EAAS8D,OAAOC,OAAOyB,QAAQmC,MACzBtE,EAAKoC,UAChB,MAEFzC,GAAQhD,EAASgD,KAEnB,OAAO8C,GAWD,YAAAH,qCAAR,SAA6CgC,EAAmBE,GAK9D,I,MAJM7H,EAAWH,KAAKI,UAChB6H,EAAU,GACVC,EAAc,CAAC,GACjB1E,EAAOrD,EAAS8D,OAAOC,OAAOyB,QAAQmC,GACnCtE,GAAM,CACX,IAAM2E,EAAWhI,EAAS8D,OAAOC,OAAOyB,QAAQmC,EAAY,GACtDM,IAAkBD,GAAWA,EAASvC,UACxCyC,EAAS7E,EAAK8E,mBAAmBF,GAAmBJ,GACxD,GAAII,GAAmBD,EAAU,CAC/B,IAAMI,EAAW/E,EAAKiE,QAAQjE,EAAKd,OAAS,GACrB6F,GAAmC,IAAvBA,EAASR,WAA2C,IAAxBQ,EAASV,YAEd,KAAjB,QAAnB,EAAAM,EAASV,QAAQ,UAAE,eAAEI,cACzCQ,EAASA,EAAOzB,MAAM,GAAI,IAI9B,GADAqB,EAAQxE,KAAK4E,IACTD,EAGF,MAFAF,EAAYzE,KAAKyE,EAAYA,EAAYxF,OAAS,GAAK2F,EAAO3F,QAIhEoF,IACAtE,EAAO2E,EAET,MAAO,CAACF,EAAQO,KAAK,IAAKN,IAQpB,YAAAvD,cAAR,SAAsB9B,EAAmC4F,EAAoCtH,GAA7F,I,IAAA,OACQhB,EAAWH,KAAKI,UAEtB,GADAJ,KAAKkC,yBACAW,EAEH,OADA1C,EAASyD,kBACF,EAGT,GADAzD,EAASuI,OAAO7F,EAAOI,IAAKJ,EAAOG,IAAKH,EAAOvB,MAC3CmH,EAAS,CACX,IAAM,EAAStI,EAASwI,gBAAgBxI,EAAS8D,OAAOC,OAAOC,MAAQhE,EAAS8D,OAAOC,OAAO0E,QAAU/F,EAAOG,KAC3G,IACFhD,KAAK6B,oBAAsB1B,EAAS0I,mBAAmB,CACrDtF,OAAM,EACNuF,EAAGjG,EAAOI,IACV8F,MAAOlG,EAAOvB,KACd0H,gBAAiBP,EAAQQ,sBACzBC,MAAO,MACPC,qBAAsB,CACpBC,MAAOX,EAAQY,iCAGK,QAAxB,EAAArJ,KAAK6B,2BAAmB,SAAEyH,UAAS,SAACC,GAAM,SAAKC,aAAaD,EAAGd,EAAQgB,mBAAmB,MAClE,QAAxB,EAAAzJ,KAAK6B,2BAAmB,SAAE6H,WAAU,WAAM,SAAOhI,cAIrD,IAAKP,IAEC0B,EAAOG,KAAQ7C,EAAS8D,OAAOC,OAAOyF,UAAYxJ,EAASiE,MAASvB,EAAOG,IAAM7C,EAAS8D,OAAOC,OAAOyF,WAAW,CACrH,IAAI,EAAS9G,EAAOG,IAAM7C,EAAS8D,OAAOC,OAAOyF,UACjD,GAAU5E,KAAK6E,MAAMzJ,EAASiE,KAAO,GACrCjE,EAAS0J,YAAY,GAGzB,OAAO,GAUD,YAAAL,aAAR,SAAqBM,EAAsBC,EAAiCC,GACtEF,EAAQG,aAAe,IAGtBH,EAAQI,UAAUC,SAAS,kCAC9BL,EAAQI,UAAUE,IAAI,gCAClBL,IACFD,EAAQO,MAAMC,QAAU,aAAaP,IAGrCC,GACFF,EAAQI,UAAUE,IAAI,yCAUlB,YAAA/G,wBAAR,SAAgCR,EAAuB4F,GAAvD,I,EAAA,OACQtI,EAAWH,KAAKI,UAChBmD,EAASpD,EAASwI,gBAAgBxI,EAAS8D,OAAOC,OAAOC,MAAQhE,EAAS8D,OAAOC,OAAO0E,QAAU/F,EAAOG,KAC/G,GAAKO,EAAL,CAGA,IAAMgH,EAAuBpK,EAAS0I,mBAAmB,CACvDtF,OAAM,EACNuF,EAAGjG,EAAOI,IACV8F,MAAOlG,EAAOvB,KACd0H,gBAAiBP,EAAQ+B,gBACzBrB,sBAA6C,QAAvB,EAAAnJ,KAAK+B,0BAAkB,eAAEgB,IAAIQ,EAAOC,YAAQvB,EAAY,CAC5EmH,MAAOX,EAAQgC,mBACfC,SAAU,YAKd,OAFAH,MAAAA,GAAAA,EAAsBjB,UAAS,SAACC,GAAM,SAAKC,aAAaD,EAAGd,EAAQkC,aAAa,MAChFJ,MAAAA,GAAAA,EAAsBb,WAAU,WAAM,OAAAnG,EAAO7B,aACtC6I,IAEX,EA3rBA,GAAa,EAAAK,YAAAA,G,kGC/Bb,8BACU,KAAAC,WAAgC,GAEhC,KAAAC,WAAqB,EAwC/B,OAtCE,sBAAW,oBAAK,C,IAAhB,sBAmBE,OAlBK9K,KAAK+K,SACR/K,KAAK+K,OAAS,SAACC,GAcb,OAbA,EAAKH,WAAWpH,KAAKuH,GACF,CACjBtJ,QAAS,WACP,IAAK,EAAKoJ,UACR,IAAK,IAAIvD,EAAI,EAAGA,EAAI,EAAKsD,WAAWnI,OAAQ6E,IAC1C,GAAI,EAAKsD,WAAWtD,KAAOyD,EAEzB,YADA,EAAKH,WAAWI,OAAO1D,EAAG,OAUjCvH,KAAK+K,Q,gCAGP,YAAAxJ,KAAP,SAAY2J,EAASC,GAEnB,IADA,IAAMC,EAA2B,GACxB7D,EAAI,EAAGA,EAAIvH,KAAK6K,WAAWnI,OAAQ6E,IAC1C6D,EAAM3H,KAAKzD,KAAK6K,WAAWtD,IAE7B,IAASA,EAAI,EAAGA,EAAI6D,EAAM1I,OAAQ6E,IAChC6D,EAAM7D,GAAG8D,UAAKpJ,EAAWiJ,EAAMC,IAI5B,YAAAzJ,QAAP,WACM1B,KAAK6K,aACP7K,KAAK6K,WAAWnI,OAAS,GAE3B1C,KAAK8K,WAAY,GAErB,EA3CA,GAAa,EAAAhL,aAAAA,EA6Cb,wBAAgCwL,EAAiBC,GAC/C,OAAOD,GAAK,SAAA/B,GAAK,OAAAgC,EAAGhK,KAAKgI,SClEvBiC,EAA2B,G,OAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBzJ,IAAjB0J,EACH,OAAOA,EAAarM,QAGrB,IAAIC,EAASiM,EAAyBE,GAAY,CAGjDpM,QAAS,IAOV,OAHAsM,EAAoBF,GAAUL,KAAK9L,EAAOD,QAASC,EAAQA,EAAOD,QAASmM,GAGpElM,EAAOD,QClBWmM,CAAoB,M", "sources": ["webpack://SearchAddon/webpack/universalModuleDefinition", "webpack://SearchAddon/./src/SearchAddon.ts", "webpack://SearchAddon/../../src/common/EventEmitter.ts", "webpack://SearchAddon/webpack/bootstrap", "webpack://SearchAddon/webpack/startup"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"SearchAddon\"] = factory();\n\telse\n\t\troot[\"SearchAddon\"] = factory();\n})(self, function() {\nreturn ", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Terminal, IDisposable, ITerminalAddon, ISelectionPosition, IDecoration } from 'xterm';\nimport { EventEmitter } from 'common/EventEmitter';\n\nexport interface ISearchOptions {\n  regex?: boolean;\n  wholeWord?: boolean;\n  caseSensitive?: boolean;\n  incremental?: boolean;\n  decorations?: ISearchDecorationOptions;\n  noScroll?: boolean;\n}\n\ninterface ISearchDecorationOptions {\n  matchBackground?: string;\n  matchBorder?: string;\n  matchOverviewRuler: string;\n  activeMatchBackground?: string;\n  activeMatchBorder?: string;\n  activeMatchColorOverviewRuler: string;\n}\n\nexport interface ISearchPosition {\n  startCol: number;\n  startRow: number;\n}\n\nexport interface ISearchResult {\n  term: string;\n  col: number;\n  row: number;\n  size: number;\n}\n\ntype LineCacheEntry = [\n  /**\n   * The string representation of a line (as opposed to the buffer cell representation).\n   */\n  lineAsString: string,\n  /**\n   * The offsets where each line starts when the entry describes a wrapped line.\n   */\n  lineOffsets: number[]\n];\n\nconst NON_WORD_CHARACTERS = ' ~!@#$%^&*()+`-=[]{}|\\\\;:\"\\',./<>?';\nconst LINES_CACHE_TIME_TO_LIVE = 15 * 1000; // 15 secs\n\nexport class SearchAddon implements ITerminalAddon {\n  private _terminal: Terminal | undefined;\n  private _cachedSearchTerm: string | undefined;\n  private _selectedDecoration: IDecoration | undefined;\n  private _resultDecorations: Map<number, IDecoration[]> | undefined;\n  private _searchResults: Map<string, ISearchResult> | undefined;\n  private _onDataDisposable: IDisposable | undefined;\n  private _onResizeDisposable: IDisposable | undefined;\n  private _lastSearchOptions: ISearchOptions | undefined;\n  private _highlightTimeout: number | undefined;\n  /**\n   * translateBufferLineToStringWithWrap is a fairly expensive call.\n   * We memoize the calls into an array that has a time based ttl.\n   * _linesCache is also invalidated when the terminal cursor moves.\n   */\n  private _linesCache: LineCacheEntry[] | undefined;\n  private _linesCacheTimeoutId = 0;\n  private _cursorMoveListener: IDisposable | undefined;\n  private _resizeListener: IDisposable | undefined;\n\n  private _resultIndex: number | undefined;\n\n  private readonly _onDidChangeResults = new EventEmitter<{ resultIndex: number, resultCount: number } | undefined>();\n  public readonly onDidChangeResults = this._onDidChangeResults.event;\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n    this._onDataDisposable = this._terminal.onWriteParsed(() => this._updateMatches());\n    this._onResizeDisposable = this._terminal.onResize(() => this._updateMatches());\n  }\n\n  private _updateMatches(): void {\n    if (this._highlightTimeout) {\n      window.clearTimeout(this._highlightTimeout);\n    }\n    if (this._cachedSearchTerm && this._lastSearchOptions?.decorations) {\n      this._highlightTimeout = setTimeout(() => {\n        this.findPrevious(this._cachedSearchTerm!, { ...this._lastSearchOptions, incremental: true, noScroll: true });\n        this._resultIndex = this._searchResults ? this._searchResults.size - 1 : -1;\n        this._onDidChangeResults.fire({ resultIndex: this._resultIndex, resultCount: this._searchResults?.size ?? -1 });\n      }, 200);\n    }\n  }\n\n  public dispose(): void {\n    this.clearDecorations();\n    this._onDataDisposable?.dispose();\n    this._onResizeDisposable?.dispose();\n  }\n\n  public clearDecorations(retainCachedSearchTerm?: boolean): void {\n    this._selectedDecoration?.dispose();\n    this._searchResults?.clear();\n    this._resultDecorations?.forEach(decorations => {\n      for (const d of decorations) {\n        d.dispose();\n      }\n    });\n    this._resultDecorations?.clear();\n    this._searchResults = undefined;\n    this._resultDecorations = undefined;\n    if (!retainCachedSearchTerm) {\n      this._cachedSearchTerm = undefined;\n    }\n  }\n\n  public clearActiveDecoration(): void {\n    this._selectedDecoration?.dispose();\n    this._selectedDecoration = undefined;\n  }\n\n  /**\n   * Find the next instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @return Whether a result was found.\n   */\n  public findNext(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._resultIndex !== undefined || this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n    return this._fireResults(term, this._findNextAndSelect(term, searchOptions), searchOptions);\n  }\n\n  private _highlightAllMatches(term: string, searchOptions: ISearchOptions): void {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    if (!term || term.length === 0) {\n      this.clearDecorations();\n      return;\n    }\n    searchOptions = searchOptions || {};\n\n    // new search, clear out the old decorations\n    this.clearDecorations(true);\n    this._searchResults = new Map<string, ISearchResult>();\n    this._resultDecorations = new Map<number, IDecoration[]>();\n    const resultDecorations = this._resultDecorations;\n    let result = this._find(term, 0, 0, searchOptions);\n    while (result && !this._searchResults.get(`${result.row}-${result.col}`)) {\n      this._searchResults.set(`${result.row}-${result.col}`, result);\n      result = this._find(\n        term,\n        result.col + result.term.length >= this._terminal.cols ? result.row + 1 : result.row,\n        result.col + result.term.length >= this._terminal.cols ? 0 : result.col + 1,\n        searchOptions\n      );\n      if (this._searchResults.size > 1000) {\n        this.clearDecorations();\n        this._resultIndex = undefined;\n        return;\n      }\n    }\n    this._searchResults.forEach(result => {\n      const resultDecoration = this._createResultDecoration(result, searchOptions.decorations!);\n      if (resultDecoration) {\n        const decorationsForLine = resultDecorations.get(resultDecoration.marker.line) || [];\n        decorationsForLine.push(resultDecoration);\n        resultDecorations.set(resultDecoration.marker.line, decorationsForLine);\n      }\n    });\n  }\n\n  private _find(term: string, startRow: number, startCol: number, searchOptions?: ISearchOptions): ISearchResult | undefined {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      return undefined;\n    }\n    if (startCol > this._terminal.cols) {\n      throw new Error(`Invalid col: ${startCol} to search in terminal of ${this._terminal.cols} cols`);\n    }\n\n    let result: ISearchResult | undefined = undefined;\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    return result;\n  }\n\n  private _findNextAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal || !term || term.length === 0) {\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      this._cachedSearchTerm = undefined;\n      this._resultIndex = -1;\n      return false;\n    }\n\n    if (this._cachedSearchTerm !== term) {\n      this._resultIndex = undefined;\n      this._terminal.clearSelection();\n    }\n\n    let startCol = 0;\n    let startRow = 0;\n    let currentSelection: ISelectionPosition | undefined;\n    if (this._terminal.hasSelection()) {\n      const incremental = searchOptions ? searchOptions.incremental : false;\n      // Start from the selection end if there is a selection\n      // For incremental search, use existing row\n      currentSelection = this._terminal.getSelectionPosition()!;\n      startRow = incremental ? currentSelection.startRow : currentSelection.endRow;\n      startCol = incremental ? currentSelection.startColumn : currentSelection.endColumn;\n    }\n\n    this._initLinesCache();\n\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    // Search startRow\n    let result = this._findInLine(term, searchPosition, searchOptions);\n    // Search from startRow + 1 to end\n    if (!result) {\n\n      for (let y = startRow + 1; y < this._terminal.buffer.active.baseY + this._terminal.rows; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        // If the current line is wrapped line, increase index of column to ignore the previous scan\n        // Otherwise, reset beginning column index to zero with set new unwrapped line index\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the bottom and didn't search from the very top wrap back up\n    if (!result && startRow !== 0) {\n      for (let y = 0; y < startRow; y++) {\n        searchPosition.startRow = y;\n        searchPosition.startCol = 0;\n        result = this._findInLine(term, searchPosition, searchOptions);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    // If there is only one result, wrap back and return selection if it exists.\n    if (!result && currentSelection) {\n      searchPosition.startRow = currentSelection.startRow;\n      searchPosition.startCol = 0;\n      result = this._findInLine(term, searchPosition, searchOptions);\n    }\n\n    if (this._searchResults) {\n      if (this._searchResults.size === 0) {\n        this._resultIndex = -1;\n      } else if (this._resultIndex === undefined) {\n        this._resultIndex = 0;\n      } else {\n        this._resultIndex++;\n        if (this._resultIndex >= this._searchResults.size) {\n          this._resultIndex = 0;\n        }\n      }\n    }\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n  /**\n   * Find the previous instance of the term, then scroll to and select it. If it\n   * doesn't exist, do nothing.\n   * @param term The search term.\n   * @param searchOptions Search options.\n   * @return Whether a result was found.\n   */\n  public findPrevious(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    this._lastSearchOptions = searchOptions;\n    if (searchOptions?.decorations) {\n      if (this._resultIndex !== undefined || this._cachedSearchTerm === undefined || term !== this._cachedSearchTerm) {\n        this._highlightAllMatches(term, searchOptions);\n      }\n    }\n    return this._fireResults(term, this._findPreviousAndSelect(term, searchOptions), searchOptions);\n  }\n\n  private _fireResults(term: string, found: boolean, searchOptions?: ISearchOptions): boolean {\n    if (searchOptions?.decorations) {\n      if (this._resultIndex !== undefined && this._searchResults?.size !== undefined) {\n        this._onDidChangeResults.fire({ resultIndex: this._resultIndex, resultCount: this._searchResults.size });\n      } else {\n        this._onDidChangeResults.fire(undefined);\n      }\n    }\n    this._cachedSearchTerm = term;\n    return found;\n  }\n\n  private _findPreviousAndSelect(term: string, searchOptions?: ISearchOptions): boolean {\n    if (!this._terminal) {\n      throw new Error('Cannot use addon until it has been loaded');\n    }\n    let result: ISearchResult | undefined;\n    if (!this._terminal || !term || term.length === 0) {\n      result = undefined;\n      this._terminal?.clearSelection();\n      this.clearDecorations();\n      this._resultIndex = -1;\n      return false;\n    }\n\n    if (this._cachedSearchTerm !== term) {\n      this._resultIndex = undefined;\n      this._terminal.clearSelection();\n    }\n\n    let startRow = this._terminal.buffer.active.baseY + this._terminal.rows;\n    let startCol = this._terminal.cols;\n    const isReverseSearch = true;\n\n    const incremental = searchOptions ? searchOptions.incremental : false;\n    let currentSelection: ISelectionPosition | undefined;\n    if (this._terminal.hasSelection()) {\n      currentSelection = this._terminal.getSelectionPosition()!;\n      // Start from selection start if there is a selection\n      startRow = currentSelection.startRow;\n      startCol = currentSelection.startColumn;\n    }\n\n    this._initLinesCache();\n    const searchPosition: ISearchPosition = {\n      startRow,\n      startCol\n    };\n\n    if (incremental) {\n      // Try to expand selection to right first.\n      result = this._findInLine(term, searchPosition, searchOptions, false);\n      const isOldResultHighlighted = result && result.row === startRow && result.col === startCol;\n      if (!isOldResultHighlighted) {\n        // If selection was not able to be expanded to the right, then try reverse search\n        if (currentSelection) {\n          searchPosition.startRow = currentSelection.endRow;\n          searchPosition.startCol = currentSelection.endColumn;\n        }\n        result = this._findInLine(term, searchPosition, searchOptions, true);\n      }\n    } else {\n      result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n    }\n\n    // Search from startRow - 1 to top\n    if (!result) {\n      searchPosition.startCol = Math.max(searchPosition.startCol, this._terminal.cols);\n      for (let y = startRow - 1; y >= 0; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n    // If we hit the top and didn't search from the very bottom wrap back down\n    if (!result && startRow !== (this._terminal.buffer.active.baseY + this._terminal.rows)) {\n      for (let y = (this._terminal.buffer.active.baseY + this._terminal.rows); y >= startRow; y--) {\n        searchPosition.startRow = y;\n        result = this._findInLine(term, searchPosition, searchOptions, isReverseSearch);\n        if (result) {\n          break;\n        }\n      }\n    }\n\n    if (this._searchResults) {\n      if (this._searchResults.size === 0) {\n        this._resultIndex = -1;\n      } else if (this._resultIndex === undefined || this._resultIndex < 0) {\n        this._resultIndex = this._searchResults.size - 1;\n      } else {\n        this._resultIndex--;\n        if (this._resultIndex === -1) {\n          this._resultIndex = this._searchResults.size - 1;\n        }\n      }\n    }\n\n    // If there is only one result, return true.\n    if (!result && currentSelection) return true;\n\n    // Set selection and scroll if a result was found\n    return this._selectResult(result, searchOptions?.decorations, searchOptions?.noScroll);\n  }\n\n  /**\n   * Sets up a line cache with a ttl\n   */\n  private _initLinesCache(): void {\n    const terminal = this._terminal!;\n    if (!this._linesCache) {\n      this._linesCache = new Array(terminal.buffer.active.length);\n      this._cursorMoveListener = terminal.onCursorMove(() => this._destroyLinesCache());\n      this._resizeListener = terminal.onResize(() => this._destroyLinesCache());\n    }\n\n    window.clearTimeout(this._linesCacheTimeoutId);\n    this._linesCacheTimeoutId = window.setTimeout(() => this._destroyLinesCache(), LINES_CACHE_TIME_TO_LIVE);\n  }\n\n  private _destroyLinesCache(): void {\n    this._linesCache = undefined;\n    if (this._cursorMoveListener) {\n      this._cursorMoveListener.dispose();\n      this._cursorMoveListener = undefined;\n    }\n    if (this._resizeListener) {\n      this._resizeListener.dispose();\n      this._resizeListener = undefined;\n    }\n    if (this._linesCacheTimeoutId) {\n      window.clearTimeout(this._linesCacheTimeoutId);\n      this._linesCacheTimeoutId = 0;\n    }\n  }\n\n  /**\n   * A found substring is a whole word if it doesn't have an alphanumeric character directly adjacent to it.\n   * @param searchIndex starting indext of the potential whole word substring\n   * @param line entire string in which the potential whole word was found\n   * @param term the substring that starts at searchIndex\n   */\n  private _isWholeWord(searchIndex: number, line: string, term: string): boolean {\n    return ((searchIndex === 0) || (NON_WORD_CHARACTERS.includes(line[searchIndex - 1]))) &&\n      (((searchIndex + term.length) === line.length) || (NON_WORD_CHARACTERS.includes(line[searchIndex + term.length])));\n  }\n\n  /**\n   * Searches a line for a search term. Takes the provided terminal line and searches the text line, which may contain\n   * subsequent terminal lines if the text is wrapped. If the provided line number is part of a wrapped text line that\n   * started on an earlier line then it is skipped since it will be properly searched when the terminal line that the\n   * text starts on is searched.\n   * @param term The search term.\n   * @param position The position to start the search.\n   * @param searchOptions Search options.\n   * @param isReverseSearch Whether the search should start from the right side of the terminal and search to the left.\n   * @return The search result if it was found.\n   */\n  protected _findInLine(term: string, searchPosition: ISearchPosition, searchOptions: ISearchOptions = {}, isReverseSearch: boolean = false): ISearchResult | undefined {\n    const terminal = this._terminal!;\n    const row = searchPosition.startRow;\n    const col = searchPosition.startCol;\n\n    // Ignore wrapped lines, only consider on unwrapped line (first row of command string).\n    const firstLine = terminal.buffer.active.getLine(row);\n    if (firstLine?.isWrapped) {\n      if (isReverseSearch) {\n        searchPosition.startCol += terminal.cols;\n        return;\n      }\n\n      // This will iterate until we find the line start.\n      // When we find it, we will search using the calculated start column.\n      searchPosition.startRow--;\n      searchPosition.startCol += terminal.cols;\n      return this._findInLine(term, searchPosition, searchOptions);\n    }\n    let cache = this._linesCache?.[row];\n    if (!cache) {\n      cache = this._translateBufferLineToStringWithWrap(row, true);\n      if (this._linesCache) {\n        this._linesCache[row] = cache;\n      }\n    }\n    const [stringLine, offsets] = cache;\n\n    const offset = this._bufferColsToStringOffset(row, col);\n    const searchTerm = searchOptions.caseSensitive ? term : term.toLowerCase();\n    const searchStringLine = searchOptions.caseSensitive ? stringLine : stringLine.toLowerCase();\n\n    let resultIndex = -1;\n    if (searchOptions.regex) {\n      const searchRegex = RegExp(searchTerm, 'g');\n      let foundTerm: RegExpExecArray | null;\n      if (isReverseSearch) {\n        // This loop will get the resultIndex of the _last_ regex match in the range 0..offset\n        while (foundTerm = searchRegex.exec(searchStringLine.slice(0, offset))) {\n          resultIndex = searchRegex.lastIndex - foundTerm[0].length;\n          term = foundTerm[0];\n          searchRegex.lastIndex -= (term.length - 1);\n        }\n      } else {\n        foundTerm = searchRegex.exec(searchStringLine.slice(offset));\n        if (foundTerm && foundTerm[0].length > 0) {\n          resultIndex = offset + (searchRegex.lastIndex - foundTerm[0].length);\n          term = foundTerm[0];\n        }\n      }\n    } else {\n      if (isReverseSearch) {\n        if (offset - searchTerm.length >= 0) {\n          resultIndex = searchStringLine.lastIndexOf(searchTerm, offset - searchTerm.length);\n        }\n      } else {\n        resultIndex = searchStringLine.indexOf(searchTerm, offset);\n      }\n    }\n\n    if (resultIndex >= 0) {\n      if (searchOptions.wholeWord && !this._isWholeWord(resultIndex, searchStringLine, term)) {\n        return;\n      }\n\n      // Adjust the row number and search index if needed since a \"line\" of text can span multiple rows\n      let startRowOffset = 0;\n      while (startRowOffset < offsets.length - 1 && resultIndex >= offsets[startRowOffset + 1]) {\n        startRowOffset++;\n      }\n      let endRowOffset = startRowOffset;\n      while (endRowOffset < offsets.length - 1 && resultIndex + term.length >= offsets[endRowOffset + 1]) {\n        endRowOffset++;\n      }\n      const startColOffset = resultIndex - offsets[startRowOffset];\n      const endColOffset = resultIndex + term.length - offsets[endRowOffset];\n      const startColIndex = this._stringLengthToBufferSize(row + startRowOffset, startColOffset);\n      const endColIndex = this._stringLengthToBufferSize(row + endRowOffset, endColOffset);\n      const size = endColIndex - startColIndex + terminal.cols * (endRowOffset - startRowOffset);\n\n      return {\n        term,\n        col: startColIndex,\n        row: row + startRowOffset,\n        size\n      };\n    }\n  }\n\n  private _stringLengthToBufferSize(row: number, offset: number): number {\n    const line = this._terminal!.buffer.active.getLine(row);\n    if (!line) {\n      return 0;\n    }\n    for (let i = 0; i < offset; i++) {\n      const cell = line.getCell(i);\n      if (!cell) {\n        break;\n      }\n      // Adjust the searchIndex to normalize emoji into single chars\n      const char = cell.getChars();\n      if (char.length > 1) {\n        offset -= char.length - 1;\n      }\n      // Adjust the searchIndex for empty characters following wide unicode\n      // chars (eg. CJK)\n      const nextCell = line.getCell(i + 1);\n      if (nextCell && nextCell.getWidth() === 0) {\n        offset++;\n      }\n    }\n    return offset;\n  }\n\n  private _bufferColsToStringOffset(startRow: number, cols: number): number {\n    const terminal = this._terminal!;\n    let lineIndex = startRow;\n    let offset = 0;\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (cols > 0 && line) {\n      for (let i = 0; i < cols && i < terminal.cols; i++) {\n        const cell = line.getCell(i);\n        if (!cell) {\n          break;\n        }\n        if (cell.getWidth()) {\n          // Treat null characters as whitespace to align with the translateToString API\n          offset += cell.getCode() === 0 ? 1 : cell.getChars().length;\n        }\n      }\n      lineIndex++;\n      line = terminal.buffer.active.getLine(lineIndex);\n      if (line && !line.isWrapped) {\n        break;\n      }\n      cols -= terminal.cols;\n    }\n    return offset;\n  }\n\n  /**\n   * Translates a buffer line to a string, including subsequent lines if they are wraps.\n   * Wide characters will count as two columns in the resulting string. This\n   * function is useful for getting the actual text underneath the raw selection\n   * position.\n   * @param line The line being translated.\n   * @param trimRight Whether to trim whitespace to the right.\n   */\n  private _translateBufferLineToStringWithWrap(lineIndex: number, trimRight: boolean): LineCacheEntry {\n    const terminal = this._terminal!;\n    const strings = [];\n    const lineOffsets = [0];\n    let line = terminal.buffer.active.getLine(lineIndex);\n    while (line) {\n      const nextLine = terminal.buffer.active.getLine(lineIndex + 1);\n      const lineWrapsToNext = nextLine ? nextLine.isWrapped : false;\n      let string = line.translateToString(!lineWrapsToNext && trimRight);\n      if (lineWrapsToNext && nextLine) {\n        const lastCell = line.getCell(line.length - 1);\n        const lastCellIsNull = lastCell && lastCell.getCode() === 0 && lastCell.getWidth() === 1;\n        // a wide character wrapped to the next line\n        if (lastCellIsNull && nextLine.getCell(0)?.getWidth() === 2) {\n          string = string.slice(0, -1);\n        }\n      }\n      strings.push(string);\n      if (lineWrapsToNext) {\n        lineOffsets.push(lineOffsets[lineOffsets.length - 1] + string.length);\n      } else {\n        break;\n      }\n      lineIndex++;\n      line = nextLine;\n    }\n    return [strings.join(''), lineOffsets];\n  }\n\n  /**\n   * Selects and scrolls to a result.\n   * @param result The result to select.\n   * @return Whether a result was selected.\n   */\n  private _selectResult(result: ISearchResult | undefined, options?: ISearchDecorationOptions, noScroll?: boolean): boolean {\n    const terminal = this._terminal!;\n    this.clearActiveDecoration();\n    if (!result) {\n      terminal.clearSelection();\n      return false;\n    }\n    terminal.select(result.col, result.row, result.size);\n    if (options) {\n      const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n      if (marker) {\n        this._selectedDecoration = terminal.registerDecoration({\n          marker,\n          x: result.col,\n          width: result.size,\n          backgroundColor: options.activeMatchBackground,\n          layer: 'top',\n          overviewRulerOptions: {\n            color: options.activeMatchColorOverviewRuler\n          }\n        });\n        this._selectedDecoration?.onRender((e) => this._applyStyles(e, options.activeMatchBorder, true));\n        this._selectedDecoration?.onDispose(() => marker.dispose());\n      }\n    }\n\n    if (!noScroll) {\n      // If it is not in the viewport then we scroll else it just gets selected\n      if (result.row >= (terminal.buffer.active.viewportY + terminal.rows) || result.row < terminal.buffer.active.viewportY) {\n        let scroll = result.row - terminal.buffer.active.viewportY;\n        scroll -= Math.floor(terminal.rows / 2);\n        terminal.scrollLines(scroll);\n      }\n    }\n    return true;\n  }\n\n  /**\n   * Applies styles to the decoration when it is rendered\n   * @param element the decoration's element\n   * @param backgroundColor the background color to apply\n   * @param borderColor the border color to apply\n   * @returns\n   */\n  private _applyStyles(element: HTMLElement, borderColor: string | undefined, isActiveResult: boolean): void {\n    if (element.clientWidth <= 0) {\n      return;\n    }\n    if (!element.classList.contains('xterm-find-result-decoration')) {\n      element.classList.add('xterm-find-result-decoration');\n      if (borderColor) {\n        element.style.outline = `1px solid ${borderColor}`;\n      }\n    }\n    if (isActiveResult) {\n      element.classList.add('xterm-find-active-result-decoration');\n    }\n  }\n\n  /**\n   * Creates a decoration for the result and applies styles\n   * @param result the search result for which to create the decoration\n   * @param options the options for the decoration\n   * @returns the {@link IDecoration} or undefined if the marker has already been disposed of\n   */\n  private _createResultDecoration(result: ISearchResult, options: ISearchDecorationOptions): IDecoration | undefined {\n    const terminal = this._terminal!;\n    const marker = terminal.registerMarker(-terminal.buffer.active.baseY - terminal.buffer.active.cursorY + result.row);\n    if (!marker) {\n      return undefined;\n    }\n    const findResultDecoration = terminal.registerDecoration({\n      marker,\n      x: result.col,\n      width: result.size,\n      backgroundColor: options.matchBackground,\n      overviewRulerOptions: this._resultDecorations?.get(marker.line) ? undefined : {\n        color: options.matchOverviewRuler,\n        position: 'center'\n      }\n    });\n    findResultDecoration?.onRender((e) => this._applyStyles(e, options.matchBorder, false));\n    findResultDecoration?.onDispose(() => marker.dispose());\n    return findResultDecoration;\n  }\n}\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { IDisposable } from 'common/Types';\n\ninterface IListener<T, U = void> {\n  (arg1: T, arg2: U): void;\n}\n\nexport interface IEvent<T, U = void> {\n  (listener: (arg1: T, arg2: U) => any): IDisposable;\n}\n\nexport interface IEventEmitter<T, U = void> {\n  event: IEvent<T, U>;\n  fire(arg1: T, arg2: U): void;\n  dispose(): void;\n}\n\nexport class EventEmitter<T, U = void> implements IEventEmitter<T, U> {\n  private _listeners: IListener<T, U>[] = [];\n  private _event?: IEvent<T, U>;\n  private _disposed: boolean = false;\n\n  public get event(): IEvent<T, U> {\n    if (!this._event) {\n      this._event = (listener: (arg1: T, arg2: U) => any) => {\n        this._listeners.push(listener);\n        const disposable = {\n          dispose: () => {\n            if (!this._disposed) {\n              for (let i = 0; i < this._listeners.length; i++) {\n                if (this._listeners[i] === listener) {\n                  this._listeners.splice(i, 1);\n                  return;\n                }\n              }\n            }\n          }\n        };\n        return disposable;\n      };\n    }\n    return this._event;\n  }\n\n  public fire(arg1: T, arg2: U): void {\n    const queue: IListener<T, U>[] = [];\n    for (let i = 0; i < this._listeners.length; i++) {\n      queue.push(this._listeners[i]);\n    }\n    for (let i = 0; i < queue.length; i++) {\n      queue[i].call(undefined, arg1, arg2);\n    }\n  }\n\n  public dispose(): void {\n    if (this._listeners) {\n      this._listeners.length = 0;\n    }\n    this._disposed = true;\n  }\n}\n\nexport function forwardEvent<T>(from: IEvent<T>, to: IEventEmitter<T>): IDisposable {\n  return from(e => to.fire(e));\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(258);\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "NON_WORD_CHARACTERS", "_linesCacheTimeoutId", "_onDidChangeResults", "EventEmitter", "onDidChangeResults", "this", "event", "activate", "terminal", "_terminal", "_onDataDisposable", "onWriteParsed", "_updateMatches", "_onResizeDisposable", "onResize", "_highlightTimeout", "window", "clearTimeout", "_cachedSearchTerm", "_lastSearchOptions", "decorations", "setTimeout", "find<PERSON>revious", "incremental", "noScroll", "_resultIndex", "_searchResults", "size", "fire", "resultIndex", "resultCount", "dispose", "clearDecorations", "retainCachedSearchTerm", "_selectedDecoration", "clear", "_resultDecorations", "for<PERSON>ach", "undefined", "clearActiveDecoration", "findNext", "term", "searchOptions", "Error", "_highlightAllMatches", "_fireResults", "_findNextAndSelect", "length", "Map", "resultDecorations", "result", "_find", "get", "row", "col", "set", "cols", "resultDecoration", "_createResultDecoration", "decorationsForLine", "marker", "line", "push", "startRow", "startCol", "clearSelection", "_initLinesCache", "searchPosition", "_findInLine", "y", "buffer", "active", "baseY", "rows", "currentSelection", "hasSelection", "getSelectionPosition", "endRow", "startColumn", "endColumn", "_selectResult", "_findPreviousAndSelect", "found", "isReverseSearch", "Math", "max", "_linesCache", "Array", "_cursorMoveListener", "onCursorMove", "_destroyLinesCache", "_resizeListener", "_isWholeWord", "searchIndex", "includes", "firstLine", "getLine", "isWrapped", "cache", "_translateBufferLineToStringWithWrap", "stringLine", "offsets", "offset", "_bufferColsToStringOffset", "searchTerm", "caseSensitive", "toLowerCase", "searchStringLine", "regex", "searchRegex", "RegExp", "foundTerm", "exec", "slice", "lastIndex", "lastIndexOf", "indexOf", "wholeWord", "startRowOffset", "endRowOffset", "startColOffset", "endColOffset", "startColIndex", "_stringLengthToBufferSize", "i", "cell", "getCell", "char", "getChars", "nextCell", "getWidth", "lineIndex", "getCode", "trimRight", "strings", "lineOffsets", "nextLine", "lineWrapsToNext", "string", "translateToString", "lastCell", "join", "options", "select", "registerMarker", "cursorY", "registerDecoration", "x", "width", "backgroundColor", "activeMatchBackground", "layer", "overviewRulerOptions", "color", "activeMatchColorOverviewRuler", "onRender", "e", "_applyStyles", "activeMatchBorder", "onDispose", "viewportY", "floor", "scrollLines", "element", "borderColor", "isActiveResult", "clientWidth", "classList", "contains", "add", "style", "outline", "findResultDecoration", "matchBackground", "matchOverviewRuler", "position", "matchBorder", "SearchAddon", "_listeners", "_disposed", "_event", "listener", "splice", "arg1", "arg2", "queue", "call", "from", "to", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__"], "sourceRoot": ""}