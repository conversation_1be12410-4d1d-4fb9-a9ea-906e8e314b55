{"version": 3, "file": "xterm-addon-web-links.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAuB,cAAID,IAE3BD,EAAoB,cAAIC,IAR1B,CASGK,MAAM,WACT,M,6HCGA,iBAEE,WACmBC,EACAC,EACAC,EACAC,QAAA,IAAAA,IAAAA,EAAA,IAHA,KAAAH,UAAAA,EACA,KAAAC,OAAAA,EACA,KAAAC,SAAAA,EACA,KAAAC,SAAAA,EAsBrB,OAjBS,YAAAC,aAAP,SAAoBC,EAAWC,GAC7B,IAAMC,EAAQC,EAAaC,YAAYJ,EAAGK,KAAKT,OAAQS,KAAKV,UAAWU,KAAKR,UAC5EI,EAASI,KAAKC,cAAcJ,KAGtB,YAAAI,cAAR,SAAsBJ,GAAtB,WACE,OAAOA,EAAMK,KAAI,SAAAC,GAQf,OAPAA,EAAKC,MAAQ,EAAKX,SAASW,MAC3BD,EAAKE,MAAQ,SAACC,EAAmBC,GAC/B,GAAI,EAAKd,SAASY,MAAO,CACf,IAAAG,EAAUL,EAAI,MACtB,EAAKV,SAASY,MAAMC,EAAOC,EAAKC,KAG7BL,MAGb,EA5BA,GAAa,EAAAM,gBAAAA,EA8Bb,+BAqGA,OApGgB,EAAAV,YAAd,SAA0BJ,EAAWe,EAAeC,EAAoBC,GAStE,IARA,IAIIC,EAJEC,EAAM,IAAIC,OAAOL,EAAMM,QAASN,EAAMO,OAAS,IAAM,KAErD,EAAyBnB,EAAaoB,qCAAqCvB,EAAI,GAAG,EAAOgB,GAAxFQ,EAAI,KAAEC,EAAc,KAGvBC,GAAe,EACbC,EAAkB,GAEY,QAA5BT,EAAQC,EAAIS,KAAKJ,KAAiB,CACxC,IAAMK,EAAOX,EAAM,GACnB,IAAKW,EAAM,CAGTC,QAAQC,IAAI,gDACZ,MASF,GAFAL,EAAcF,EAAKQ,QAAQH,EAAMH,EAAc,GAC/CP,EAAIc,UAAYP,EAAcG,EAAKK,OAC/BR,EAAc,EAEhB,MAMF,IAHA,IAAIS,EAAOT,EAAcG,EAAKK,OAC1BE,EAAOX,EAAiB,EAErBU,EAAOnB,EAASqB,MACrBF,GAAQnB,EAASqB,KACjBD,IAKF,IAFA,IAAIE,EAASZ,EAAc,EACvBa,EAASd,EAAiB,EACvBa,EAAStB,EAASqB,MACvBC,GAAUtB,EAASqB,KACnBE,IAGF,IAAM1B,EAAQ,CACZ2B,MAAO,CACLC,EAAGH,EACHtC,EAAGuC,GAELG,IAAK,CACHD,EAAGN,EACHnC,EAAGoC,IAIPT,EAAOgB,KAAK,CAAE9B,MAAK,EAAEgB,KAAI,EAAEZ,SAAQ,IAGrC,OAAOU,GASM,EAAAJ,qCAAf,SAAoDqB,EAAmBC,EAAoB7B,GACzF,IACI8B,EACAC,EAFAC,EAAa,GAIjB,EAAG,CAED,KADMxB,EAAOR,EAASiC,OAAOC,OAAOC,QAAQP,IAE1C,MAGEpB,EAAK4B,WACPR,IAGFG,EAAkBvB,EAAK4B,gBAChBL,GAET,IAAMtB,EAAiBmB,EAEvB,EAAG,CACD,IAEMpB,EAFA6B,EAAWrC,EAASiC,OAAOC,OAAOC,QAAQP,EAAY,GAG5D,GAFAE,IAAkBO,GAAWA,EAASD,YAChC5B,EAAOR,EAASiC,OAAOC,OAAOC,QAAQP,IAE1C,MAEFI,GAAcxB,EAAK8B,mBAAmBR,GAAmBD,GAAWU,UAAU,EAAGvC,EAASqB,MAC1FO,UACOE,GAET,MAAO,CAACE,EAAYvB,IAExB,EArGA,GAAa,EAAAtB,aAAAA,IC1CTqD,EAA2B,GAG/B,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAarE,QAGrB,IAAIC,EAASiE,EAAyBE,GAAY,CAGjDpE,QAAS,IAOV,OAHAuE,EAAoBH,GAAUnE,EAAQA,EAAOD,QAASmE,GAG/ClE,EAAOD,Q,qGCff,WAoBMwE,EAAiB,IAAI1C,OAAOoB,gTAElC,SAASuB,EAAWpD,EAAmBC,GACrC,IAAMoD,EAAYC,OAAOC,OACzB,GAAIF,EAAW,CACb,IACEA,EAAUG,OAAS,KACnB,UAGFH,EAAUI,SAASC,KAAOzD,OAE1BkB,QAAQwC,KAAK,uDAIjB,iBAKE,WACUzE,EACAC,EACAyE,QAFA,IAAA1E,IAAAA,EAAA,QACA,IAAAC,IAAAA,EAAA,SACA,IAAAyE,IAAAA,GAAA,GAFA,KAAA1E,SAAAA,EACA,KAAAC,SAAAA,EACA,KAAAyE,iBAAAA,EA0BZ,OAtBS,YAAAtD,SAAP,SAAgBD,GAGd,GAFAX,KAAKV,UAAYqB,EAEbX,KAAKkE,kBAAoB,yBAA0BlE,KAAKV,UAAW,CACrE,IACMoB,GADAyD,EAAUnE,KAAKP,UACC2E,UAAYX,EAClCzD,KAAKqE,cAAgBrE,KAAKV,UAAUgF,qBAAqB,IAAI,EAAA7D,gBAAgBT,KAAKV,UAAWoB,EAAOV,KAAKR,SAAU2E,QAC9G,CAEL,IAAMA,GAAAA,EAAUnE,KAAKP,UACb8E,WAAa,EACrBvE,KAAKwE,eAAkBxE,KAAKV,UAAuBmF,oBAAoBhB,EAAgBzD,KAAKR,SAAU2E,KAInG,YAAAO,QAAP,W,WAC8BnB,IAAxBvD,KAAKwE,qBAAmDjB,IAAnBvD,KAAKV,WAC5CU,KAAKV,UAAUqF,sBAAsB3E,KAAKwE,gBAG1B,QAAlB,EAAAxE,KAAKqE,qBAAa,SAAEK,WAExB,EAlCA,GAAa,EAAAE,cAAAA,G", "sources": ["webpack://WebLinksAddon/webpack/universalModuleDefinition", "webpack://WebLinksAddon/./src/WebLinkProvider.ts", "webpack://WebLinksAddon/webpack/bootstrap", "webpack://WebLinksAddon/./src/WebLinksAddon.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"WebLinksAddon\"] = factory();\n\telse\n\t\troot[\"WebLinksAddon\"] = factory();\n})(self, function() {\nreturn ", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { ILinkProvider, ILink, Terminal, IViewportRange } from 'xterm';\n\nexport interface ILinkProviderOptions {\n  hover?(event: MouseEvent, text: string, location: IViewportRange): void;\n  leave?(event: MouseEvent, text: string): void;\n  urlRegex?: RegExp;\n}\n\nexport class WebLinkProvider implements ILinkProvider {\n\n  constructor(\n    private readonly _terminal: Terminal,\n    private readonly _regex: RegExp,\n    private readonly _handler: (event: MouseEvent, uri: string) => void,\n    private readonly _options: ILinkProviderOptions = {}\n  ) {\n\n  }\n\n  public provideLinks(y: number, callback: (links: ILink[] | undefined) => void): void {\n    const links = LinkComputer.computeLink(y, this._regex, this._terminal, this._handler);\n    callback(this._addCallbacks(links));\n  }\n\n  private _addCallbacks(links: ILink[]): ILink[] {\n    return links.map(link => {\n      link.leave = this._options.leave;\n      link.hover = (event: MouseEvent, uri: string): void => {\n        if (this._options.hover) {\n          const { range } = link;\n          this._options.hover(event, uri, range);\n        }\n      };\n      return link;\n    });\n  }\n}\n\nexport class LinkComputer {\n  public static computeLink(y: number, regex: RegExp, terminal: Terminal, activate: (event: MouseEvent, uri: string) => void): ILink[] {\n    const rex = new RegExp(regex.source, (regex.flags || '') + 'g');\n\n    const [line, startLineIndex] = LinkComputer._translateBufferLineToStringWithWrap(y - 1, false, terminal);\n\n    let match;\n    let stringIndex = -1;\n    const result: ILink[] = [];\n\n    while ((match = rex.exec(line)) !== null) {\n      const text = match[1];\n      if (!text) {\n        // something matched but does not comply with the given matchIndex\n        // since this is most likely a bug the regex itself we simply do nothing here\n        console.log('match found without corresponding matchIndex');\n        break;\n      }\n\n      // Get index, match.index is for the outer match which includes negated chars\n      // therefore we cannot use match.index directly, instead we search the position\n      // of the match group in text again\n      // also correct regex and string search offsets for the next loop run\n      stringIndex = line.indexOf(text, stringIndex + 1);\n      rex.lastIndex = stringIndex + text.length;\n      if (stringIndex < 0) {\n        // invalid stringIndex (should not have happened)\n        break;\n      }\n\n      let endX = stringIndex + text.length;\n      let endY = startLineIndex + 1;\n\n      while (endX > terminal.cols) {\n        endX -= terminal.cols;\n        endY++;\n      }\n\n      let startX = stringIndex + 1;\n      let startY = startLineIndex + 1;\n      while (startX > terminal.cols) {\n        startX -= terminal.cols;\n        startY++;\n      }\n\n      const range = {\n        start: {\n          x: startX,\n          y: startY\n        },\n        end: {\n          x: endX,\n          y: endY\n        }\n      };\n\n      result.push({ range, text, activate });\n    }\n\n    return result;\n  }\n\n  /**\n   * Gets the entire line for the buffer line\n   * @param line The line being translated.\n   * @param trimRight Whether to trim whitespace to the right.\n   * @param terminal The terminal\n   */\n  private static _translateBufferLineToStringWithWrap(lineIndex: number, trimRight: boolean, terminal: Terminal): [string, number] {\n    let lineString = '';\n    let lineWrapsToNext: boolean;\n    let prevLinesToWrap: boolean;\n\n    do {\n      const line = terminal.buffer.active.getLine(lineIndex);\n      if (!line) {\n        break;\n      }\n\n      if (line.isWrapped) {\n        lineIndex--;\n      }\n\n      prevLinesToWrap = line.isWrapped;\n    } while (prevLinesToWrap);\n\n    const startLineIndex = lineIndex;\n\n    do {\n      const nextLine = terminal.buffer.active.getLine(lineIndex + 1);\n      lineWrapsToNext = nextLine ? nextLine.isWrapped : false;\n      const line = terminal.buffer.active.getLine(lineIndex);\n      if (!line) {\n        break;\n      }\n      lineString += line.translateToString(!lineWrapsToNext && trimRight).substring(0, terminal.cols);\n      lineIndex++;\n    } while (lineWrapsToNext);\n\n    return [lineString, startLineIndex];\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "/**\n * Copyright (c) 2019 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Terminal, ILinkMatcherOptions, ITerminalAddon, IDisposable } from 'xterm';\nimport { ILinkProviderOptions, WebLinkProvider } from './WebLinkProvider';\n\nconst protocolClause = '(https?:\\\\/\\\\/)';\nconst domainCharacterSet = '[\\\\da-z\\\\.-]+';\nconst negatedDomainCharacterSet = '[^\\\\da-z\\\\.-]+';\nconst domainBodyClause = '(' + domainCharacterSet + ')';\nconst tldClause = '([a-z\\\\.]{2,18})';\nconst ipClause = '((\\\\d{1,3}\\\\.){3}\\\\d{1,3})';\nconst localHostClause = '(localhost)';\nconst portClause = '(:\\\\d{1,5})';\nconst hostClause = '((' + domainBodyClause + '\\\\.' + tldClause + ')|' + ipClause + '|' + localHostClause + ')' + portClause + '?';\nconst pathCharacterSet = '(\\\\/[\\\\/\\\\w\\\\.\\\\-%~:+@]*)*([^:\"\\'\\\\s])';\nconst pathClause = '(' + pathCharacterSet + ')?';\nconst queryStringHashFragmentCharacterSet = '[0-9\\\\w\\\\[\\\\]\\\\(\\\\)\\\\/\\\\?\\\\!#@$%&\\'*+,:;~\\\\=\\\\.\\\\-]*';\nconst queryStringClause = '(\\\\?' + queryStringHashFragmentCharacterSet + ')?';\nconst hashFragmentClause = '(#' + queryStringHashFragmentCharacterSet + ')?';\nconst negatedPathCharacterSet = '[^\\\\/\\\\w\\\\.\\\\-%]+';\nconst bodyClause = hostClause + pathClause + queryStringClause + hashFragmentClause;\nconst start = '(?:^|' + negatedDomainCharacterSet + ')(';\nconst end = ')($|' + negatedPathCharacterSet + ')';\nconst strictUrlRegex = new RegExp(start + protocolClause + bodyClause + end);\n\nfunction handleLink(event: MouseEvent, uri: string): void {\n  const newWindow = window.open();\n  if (newWindow) {\n    try {\n      newWindow.opener = null;\n    } catch {\n      // no-op, Electron can throw\n    }\n    newWindow.location.href = uri;\n  } else {\n    console.warn('Opening link blocked as opener could not be cleared');\n  }\n}\n\nexport class WebLinksAddon implements ITerminalAddon {\n  private _linkMatcherId: number | undefined;\n  private _terminal: Terminal | undefined;\n  private _linkProvider: IDisposable | undefined;\n\n  constructor(\n    private _handler: (event: MouseEvent, uri: string) => void = handleLink,\n    private _options: ILinkMatcherOptions | ILinkProviderOptions = {},\n    private _useLinkProvider: boolean = false\n  ) {\n  }\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n\n    if (this._useLinkProvider && 'registerLinkProvider' in this._terminal) {\n      const options = this._options as ILinkProviderOptions;\n      const regex = options.urlRegex || strictUrlRegex;\n      this._linkProvider = this._terminal.registerLinkProvider(new WebLinkProvider(this._terminal, regex, this._handler, options));\n    } else {\n      // TODO: This should be removed eventually\n      const options = this._options as ILinkMatcherOptions;\n      options.matchIndex = 1;\n      this._linkMatcherId = (this._terminal as Terminal).registerLinkMatcher(strictUrlRegex, this._handler, options);\n    }\n  }\n\n  public dispose(): void {\n    if (this._linkMatcherId !== undefined && this._terminal !== undefined) {\n      this._terminal.deregisterLinkMatcher(this._linkMatcherId);\n    }\n\n    this._linkProvider?.dispose();\n  }\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "_terminal", "_regex", "_handler", "_options", "provideLinks", "y", "callback", "links", "LinkComputer", "computeLink", "this", "_addCallbacks", "map", "link", "leave", "hover", "event", "uri", "range", "WebLinkProvider", "regex", "terminal", "activate", "match", "rex", "RegExp", "source", "flags", "_translateBufferLineToStringWithWrap", "line", "startLineIndex", "stringIndex", "result", "exec", "text", "console", "log", "indexOf", "lastIndex", "length", "endX", "endY", "cols", "startX", "startY", "start", "x", "end", "push", "lineIndex", "trimRight", "lineWrapsToNext", "prevLinesToWrap", "lineString", "buffer", "active", "getLine", "isWrapped", "nextLine", "translateToString", "substring", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "__webpack_modules__", "strictUrlRegex", "handleLink", "newWindow", "window", "open", "opener", "location", "href", "warn", "_useLinkProvider", "options", "urlRegex", "_linkProvider", "registerLinkProvider", "matchIndex", "_linkMatcherId", "registerLinkMatcher", "dispose", "deregisterLinkMatcher", "WebLinksAddon"], "sourceRoot": ""}