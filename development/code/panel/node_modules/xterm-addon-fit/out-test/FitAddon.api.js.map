{"version": 3, "file": "FitAddon.api.js", "sourceRoot": "", "sources": ["../test/FitAddon.api.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,6BAA8B;AAC9B,6DAA+E;AAG/E,IAAM,GAAG,GAAG,4BAA4B,CAAC;AAEzC,IAAI,OAAgB,CAAC;AACrB,IAAI,IAAU,CAAC;AACf,IAAM,KAAK,GAAG,IAAI,CAAC;AACnB,IAAM,MAAM,GAAG,GAAG,CAAC;AAEnB,QAAQ,CAAC,UAAU,EAAE;IACnB,MAAM,CAAC;;;;;;wBACC,WAAW,GAAG,0BAAc,EAAE,CAAC;wBAC3B,WAAM,WAAW,CAAC,MAAM,CAAC;gCACjC,QAAQ,EAAE,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;6BACpD,CAAC,EAAA;;wBAFF,OAAO,GAAG,SAER,CAAC;wBAC<PERSON>,WAAM,OAAO,CAAC,UAAU,EAAE,EAAA;4BAAjC,WAAM,CAAC,SAA0B,CAAC,CAAC,OAAO,EAAE,EAAA;;wBAAnD,IAAI,GAAG,SAA4C,CAAC;wBACpD,WAAM,IAAI,CAAC,eAAe,CAAC,EAAE,KAAK,OAAA,EAAE,MAAM,QAAA,EAAE,CAAC,EAAA;;wBAA7C,SAA6C,CAAC;wBAC9C,WAAM,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAA;;wBAApB,SAAoB,CAAC;;;;;KACtB,CAAC,CAAC;IAEH,UAAU,CAAC;;;;4BACT,WAAM,IAAI,CAAC,QAAQ,CAAC,gEAAgE,CAAC,EAAA;;wBAArF,SAAqF,CAAC;wBACtF,WAAM,wBAAY,CAAC,IAAI,CAAC,EAAA;;wBAAxB,SAAwB,CAAC;;;;;KAC1B,CAAC,CAAC;IAEH,KAAK,CAAC;;;wBACJ,WAAM,OAAO,CAAC,KAAK,EAAE,EAAA;;oBAArB,SAAqB,CAAC;;;;SACvB,CAAC,CAAC;IAEH,SAAS,CAAC;;;;4BACR,WAAM,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAA;;wBAA5C,SAA4C,CAAC;;;;;KAC9C,CAAC,CAAC;IAEH,EAAE,CAAC,aAAa,EAAE;;;;;4BAChB,WAAM,IAAI,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAA;;wBAAnD,SAAmD,CAAC;wBACpD,KAAA,CAAA,KAAA,aAAM,CAAA,CAAC,KAAK,CAAA;wBAAC,WAAM,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAA;;wBAAlE,cAAa,SAAqD,EAAE,SAAS,EAAC,CAAC;;;;;KAChF,CAAC,CAAC;IAEH,QAAQ,CAAC,mBAAmB,EAAE;QAC5B,SAAS,CAAC;;gBACR,WAAO,SAAS,EAAE,EAAC;;aACpB,CAAC,CAAC;QAEH,EAAE,CAAC,SAAS,EAAE;;;;;gCACZ,WAAM,OAAO,EAAE,EAAA;;4BAAf,SAAe,CAAC;4BACiC,WAAM,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAA;;4BAAhG,UAAU,GAAiC,SAAqD;4BACtG,aAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BAClC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BACpC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;;;;;SACrC,CAAC,CAAC;QAEH,EAAE,CAAC,OAAO,EAAE;;;;;gCACV,WAAM,OAAO,CAAC,IAAI,CAAC,EAAA;;4BAAnB,SAAmB,CAAC;4BAC6B,WAAM,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAA;;4BAAhG,UAAU,GAAiC,SAAqD;4BACtG,aAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;4BACnC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BACpC,aAAM,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;;;;;SACrC,CAAC,CAAC;QAEH,EAAE,CAAC,OAAO,EAAE;;;;;gCACV,WAAM,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA;;4BAAnB,SAAmB,CAAC;4BACpB,KAAA,CAAA,KAAA,aAAM,CAAA,CAAC,SAAS,CAAA;4BAAC,WAAM,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAA;;4BAAtE,cAAiB,SAAqD,EAAE;oCACtE,IAAI,EAAE,CAAC;oCACP,IAAI,EAAE,CAAC;iCACR,EAAC,CAAC;;;;;SACJ,CAAC,CAAC;QAEH,EAAE,CAAC,QAAQ,EAAE;;;;;gCACX,WAAM,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAA;;4BAA5C,SAA4C,CAAC;4BAC7C,WAAM,IAAI,CAAC,QAAQ,CAAC,oEAAoE,CAAC,EAAA;;4BAAzF,SAAyF,CAAC;4BAC1F,WAAM,IAAI,CAAC,QAAQ,CAAC,8BAA8B,CAAC,EAAA;;4BAAnD,SAAmD,CAAC;4BACpD,WAAM,IAAI,CAAC,QAAQ,CAAC,iEAAiE,CAAC,EAAA;;4BAAtF,SAAsF,CAAC;4BACvF,WAAM,OAAO,EAAE,EAAA;;4BAAf,SAAe,CAAC;4BAChB,KAAA,CAAA,KAAA,aAAM,CAAA,CAAC,KAAK,CAAA;4BAAC,WAAM,IAAI,CAAC,QAAQ,CAAC,gCAAgC,CAAC,EAAA;;4BAAlE,cAAa,SAAqD,EAAE,SAAS,EAAC,CAAC;;;;;SAChF,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,EAAE;QACd,SAAS,CAAC;;gBACR,WAAO,SAAS,EAAE,EAAC;;aACpB,CAAC,CAAC;QAEH,EAAE,CAAC,SAAS,EAAE;;;;;gCACZ,WAAM,OAAO,EAAE,EAAA;;4BAAf,SAAe,CAAC;4BAChB,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAvC,SAAuC,CAAC;4BACnB,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAtD,IAAI,GAAW,SAAuC;4BACvC,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAtD,IAAI,GAAW,SAAuC;4BAC5D,aAAM,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BACvB,aAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BACzB,aAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;;;;;SAC1B,CAAC,CAAC;QAEH,EAAE,CAAC,OAAO,EAAE;;;;;gCACV,WAAM,OAAO,CAAC,IAAI,CAAC,EAAA;;4BAAnB,SAAmB,CAAC;4BACpB,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAvC,SAAuC,CAAC;4BACnB,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAtD,IAAI,GAAW,SAAuC;4BACvC,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAtD,IAAI,GAAW,SAAuC;4BAC5D,aAAM,CAAC,KAAK,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;4BACxB,aAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;4BACzB,aAAM,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;;;;;SAC1B,CAAC,CAAC;QAEH,EAAE,CAAC,OAAO,EAAE;;;;;gCACV,WAAM,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAA;;4BAAnB,SAAmB,CAAC;4BACpB,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAAvC,SAAuC,CAAC;4BACxC,KAAA,CAAA,KAAA,aAAM,CAAA,CAAC,KAAK,CAAA;4BAAC,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAApD,cAAa,SAAuC,EAAE,CAAC,EAAC,CAAC;4BACzD,KAAA,CAAA,KAAA,aAAM,CAAA,CAAC,KAAK,CAAA;4BAAC,WAAM,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAA;;4BAApD,cAAa,SAAuC,EAAE,CAAC,EAAC,CAAC;;;;;SAC1D,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AAEH,SAAe,OAAO,CAAC,KAAmB,EAAE,MAAoB;IAAzC,sBAAA,EAAA,WAAmB;IAAE,uBAAA,EAAA,YAAoB;;;;wBAC9D,WAAM,IAAI,CAAC,QAAQ,CAAC,gJAG2C,KAAK,8EACJ,MAAM,aACrE,CAAC,EAAA;;oBALF,SAKE,CAAC;;;;;CACJ;AAED,SAAe,SAAS;;;;wBACtB,WAAM,IAAI,CAAC,QAAQ,CAAC,uBAAuB,CAAC,EAAA;;oBAA5C,SAA4C,CAAC;;;;;CAC9C"}