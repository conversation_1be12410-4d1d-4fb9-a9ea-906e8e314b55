{"version": 3, "file": "FitAddon.js", "sourceRoot": "", "sources": ["../src/FitAddon.ts"], "names": [], "mappings": ";;;AAmBA,IAAM,YAAY,GAAG,CAAC,CAAC;AACvB,IAAM,YAAY,GAAG,CAAC,CAAC;AAEvB;IAGE;IAAe,CAAC;IAET,2BAAQ,GAAf,UAAgB,QAAkB;QAChC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC;IAC5B,CAAC;IAEM,0BAAO,GAAd,cAAwB,CAAC;IAElB,sBAAG,GAAV;QACE,IAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACtC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC5B,OAAO;SACR;QAGD,IAAM,IAAI,GAAI,IAAI,CAAC,SAAiB,CAAC,KAAK,CAAC;QAG3C,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YAC1E,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC5B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;IACH,CAAC;IAEM,oCAAiB,GAAxB;QACE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,OAAO,SAAS,CAAC;SAClB;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,EAAE;YACpE,OAAO,SAAS,CAAC;SAClB;QAGD,IAAM,IAAI,GAAI,IAAI,CAAC,SAAiB,CAAC,KAAK,CAAC;QAE3C,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,KAAK,CAAC,IAAI,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,KAAK,CAAC,EAAE;YACjH,OAAO,SAAS,CAAC;SAClB;QAED,IAAM,kBAAkB,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QACzF,IAAM,mBAAmB,GAAG,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpF,IAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,QAAQ,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QAC/F,IAAM,YAAY,GAAG,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QACrE,IAAM,cAAc,GAAG;YACrB,GAAG,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;YAC3D,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;YACjE,KAAK,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,eAAe,CAAC,CAAC;YAC/D,IAAI,EAAE,QAAQ,CAAC,YAAY,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC;SAC9D,CAAC;QACF,IAAM,iBAAiB,GAAG,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC;QACrE,IAAM,iBAAiB,GAAG,cAAc,CAAC,KAAK,GAAG,cAAc,CAAC,IAAI,CAAC;QACrE,IAAM,eAAe,GAAG,mBAAmB,GAAG,iBAAiB,CAAC;QAChE,IAAM,cAAc,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC;QAC7F,IAAM,QAAQ,GAAG;YACf,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC;YACzG,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,gBAAgB,CAAC,CAAC;SAC5G,CAAC;QACF,OAAO,QAAQ,CAAC;IAClB,CAAC;IACH,eAAC;AAAD,CAAC,AA/DD,IA+DC;AA/DY,4BAAQ"}