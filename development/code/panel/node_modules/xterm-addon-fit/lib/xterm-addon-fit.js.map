{"version": 3, "sources": ["webpack://FitAddon/webpack/universalModuleDefinition", "webpack://FitAddon/./src/FitAddon.ts", "webpack://FitAddon/webpack/bootstrap", "webpack://FitAddon/webpack/startup"], "names": ["root", "factory", "exports", "module", "define", "amd", "self", "activate", "terminal", "this", "_terminal", "dispose", "fit", "dims", "proposeDimensions", "core", "_core", "rows", "cols", "_renderService", "clear", "resize", "element", "parentElement", "dimensions", "actual<PERSON>ell<PERSON><PERSON><PERSON>", "actualCellHeight", "parentElementStyle", "window", "getComputedStyle", "parentElementHeight", "parseInt", "getPropertyValue", "parent<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "max", "elementStyle", "availableHeight", "availableWidth", "viewport", "scrollBarWidth", "floor", "FitAddon", "__webpack_module_cache__", "__webpack_require__", "moduleId", "__webpack_modules__"], "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAkB,SAAID,IAEtBD,EAAe,SAAIC,IARrB,CASGK,MAAM,WACT,M,yGCSA,IAGA,aAGE,cA4DF,OA1DS,YAAAC,SAAP,SAAgBC,GACdC,KAAKC,UAAYF,GAGZ,YAAAG,QAAP,aAEO,YAAAC,IAAP,WACE,IAAMC,EAAOJ,KAAKK,oBAClB,GAAKD,GAASJ,KAAKC,UAAnB,CAKA,IAAMK,EAAQN,KAAKC,UAAkBM,MAGjCP,KAAKC,UAAUO,OAASJ,EAAKI,MAAQR,KAAKC,UAAUQ,OAASL,EAAKK,OACpEH,EAAKI,eAAeC,QACpBX,KAAKC,UAAUW,OAAOR,EAAKK,KAAML,EAAKI,SAInC,YAAAH,kBAAP,WACE,GAAKL,KAAKC,WAILD,KAAKC,UAAUY,SAAYb,KAAKC,UAAUY,QAAQC,cAAvD,CAKA,IAAMR,EAAQN,KAAKC,UAAkBM,MAErC,GAAuD,IAAnDD,EAAKI,eAAeK,WAAWC,iBAA6E,IAApDV,EAAKI,eAAeK,WAAWE,iBAA3F,CAIA,IAAMC,EAAqBC,OAAOC,iBAAiBpB,KAAKC,UAAUY,QAAQC,eACpEO,EAAsBC,SAASJ,EAAmBK,iBAAiB,WACnEC,EAAqBC,KAAKC,IAAI,EAAGJ,SAASJ,EAAmBK,iBAAiB,WAC9EI,EAAeR,OAAOC,iBAAiBpB,KAAKC,UAAUY,SAStDe,EAAkBP,GAPjBC,SAASK,EAAaJ,iBAAiB,gBACpCD,SAASK,EAAaJ,iBAAiB,oBAO3CM,EAAiBL,GANdF,SAASK,EAAaJ,iBAAiB,kBACxCD,SAASK,EAAaJ,iBAAiB,kBAKiBjB,EAAKwB,SAASC,eAK9E,MAJiB,CACftB,KAAMgB,KAAKC,IA7DI,EA6DcD,KAAKO,MAAMH,EAAiBvB,EAAKI,eAAeK,WAAWC,kBACxFR,KAAMiB,KAAKC,IA7DI,EA6DcD,KAAKO,MAAMJ,EAAkBtB,EAAKI,eAAeK,WAAWE,uBAI/F,EA/DA,GAAa,EAAAgB,aCrBTC,EAA2B,GCE/B,ODCA,SAASC,EAAoBC,GAE5B,GAAGF,EAAyBE,GAC3B,OAAOF,EAAyBE,GAAU3C,QAG3C,IAAIC,EAASwC,EAAyBE,GAAY,CAGjD3C,QAAS,IAOV,OAHA4C,EAAoBD,GAAU1C,EAAQA,EAAOD,QAAS0C,GAG/CzC,EAAOD,QCjBR0C,CAAoB,M", "file": "xterm-addon-fit.js", "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"FitAddon\"] = factory();\n\telse\n\t\troot[\"FitAddon\"] = factory();\n})(self, function() {\nreturn ", "/**\n * Copyright (c) 2017 The xterm.js authors. All rights reserved.\n * @license MIT\n */\n\nimport { Terminal, ITerminalAddon } from 'xterm';\n\ninterface ITerminalDimensions {\n  /**\n   * The number of rows in the terminal.\n   */\n  rows: number;\n\n  /**\n   * The number of columns in the terminal.\n   */\n  cols: number;\n}\n\nconst MINIMUM_COLS = 2;\nconst MINIMUM_ROWS = 1;\n\nexport class FitAddon implements ITerminalAddon {\n  private _terminal: Terminal | undefined;\n\n  constructor() {}\n\n  public activate(terminal: Terminal): void {\n    this._terminal = terminal;\n  }\n\n  public dispose(): void {}\n\n  public fit(): void {\n    const dims = this.proposeDimensions();\n    if (!dims || !this._terminal) {\n      return;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n\n    // Force a full render\n    if (this._terminal.rows !== dims.rows || this._terminal.cols !== dims.cols) {\n      core._renderService.clear();\n      this._terminal.resize(dims.cols, dims.rows);\n    }\n  }\n\n  public proposeDimensions(): ITerminalDimensions | undefined {\n    if (!this._terminal) {\n      return undefined;\n    }\n\n    if (!this._terminal.element || !this._terminal.element.parentElement) {\n      return undefined;\n    }\n\n    // TODO: Remove reliance on private API\n    const core = (this._terminal as any)._core;\n\n    if (core._renderService.dimensions.actualCellWidth === 0 || core._renderService.dimensions.actualCellHeight === 0) {\n      return undefined;\n    }\n\n    const parentElementStyle = window.getComputedStyle(this._terminal.element.parentElement);\n    const parentElementHeight = parseInt(parentElementStyle.getPropertyValue('height'));\n    const parentElementWidth = Math.max(0, parseInt(parentElementStyle.getPropertyValue('width')));\n    const elementStyle = window.getComputedStyle(this._terminal.element);\n    const elementPadding = {\n      top: parseInt(elementStyle.getPropertyValue('padding-top')),\n      bottom: parseInt(elementStyle.getPropertyValue('padding-bottom')),\n      right: parseInt(elementStyle.getPropertyValue('padding-right')),\n      left: parseInt(elementStyle.getPropertyValue('padding-left'))\n    };\n    const elementPaddingVer = elementPadding.top + elementPadding.bottom;\n    const elementPaddingHor = elementPadding.right + elementPadding.left;\n    const availableHeight = parentElementHeight - elementPaddingVer;\n    const availableWidth = parentElementWidth - elementPaddingHor - core.viewport.scrollBarWidth;\n    const geometry = {\n      cols: Math.max(MINIMUM_COLS, Math.floor(availableWidth / core._renderService.dimensions.actualCellWidth)),\n      rows: Math.max(MINIMUM_ROWS, Math.floor(availableHeight / core._renderService.dimensions.actualCellHeight))\n    };\n    return geometry;\n  }\n}\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tif(__webpack_module_cache__[moduleId]) {\n\t\treturn __webpack_module_cache__[moduleId].exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// module exports must be returned from runtime so entry inlining is disabled\n// startup\n// Load entry module and return exports\nreturn __webpack_require__(775);\n"], "sourceRoot": ""}