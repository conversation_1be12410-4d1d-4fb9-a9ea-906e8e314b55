{"title": "IgnorePluginOptions", "oneOf": [{"type": "object", "additionalProperties": false, "properties": {"contextRegExp": {"description": "A RegExp to test the context (directory) against", "instanceof": "RegExp", "tsType": "RegExp"}, "resourceRegExp": {"description": "A RegExp to test the request against", "instanceof": "RegExp", "tsType": "RegExp"}}}, {"type": "object", "additionalProperties": false, "properties": {"checkContext": {"description": "A filter function for context", "instanceof": "Function", "tsType": "((context: string) => boolean)"}, "checkResource": {"description": "A filter function for resource and context", "instanceof": "Function", "tsType": "((resource: string, context: string) => boolean)"}}}]}